<script lang="ts" setup>
interface Props {
  src: string
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '景点语音介绍'
})

// 音频上下文
const innerAudioContext = ref<UniApp.InnerAudioContext | null>(null)

// 播放状态
const isPlaying = ref(false)
const currentTime = ref(0) // 当前播放时间(秒)
const duration = ref(0) // 音频总时长(秒)

// 进度条元素引用
const progressBarRef = ref<HTMLElement | null>(null)

// 格式化时间显示
const formatTime = (time: number): string => {
  const minutes = Math.floor(time / 60)
  const seconds = Math.floor(time % 60)
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 进度百分比
const progress = computed(() => {
  if (duration.value <= 0) return 0
  return (currentTime.value / duration.value) * 100
})

onMounted(() => {
  // 创建音频上下文
  innerAudioContext.value = uni.createInnerAudioContext()
  innerAudioContext.value.src = props.src
  
  // 监听音频播放事件
  innerAudioContext.value.onPlay(() => {
    isPlaying.value = true
  })
  
  // 监听音频暂停事件
  innerAudioContext.value.onPause(() => {
    isPlaying.value = false
  })
  
  // 监听音频停止事件
  innerAudioContext.value.onStop(() => {
    isPlaying.value = false
    currentTime.value = 0
  })
  
  // 监听音频结束事件
  innerAudioContext.value.onEnded(() => {
    isPlaying.value = false
    currentTime.value = 0
  })
  
  // 监听播放进度更新事件
  innerAudioContext.value.onTimeUpdate(() => {
    currentTime.value = innerAudioContext.value!.currentTime
    if (duration.value === 0) {
      duration.value = innerAudioContext.value!.duration
    }
  })
  
  // 监听音频加载完成事件
  innerAudioContext.value.onCanplay(() => {
    duration.value = innerAudioContext.value!.duration
  })
  
  // 监听错误事件
  innerAudioContext.value.onError((res) => {
    console.error('音频播放错误:', res)
    uni.showToast({
      title: '音频播放失败',
      icon: 'none'
    })
  })
})

onUnmounted(() => {
  // 销毁音频上下文
  if (innerAudioContext.value) {
    innerAudioContext.value.destroy()
  }
})

// 播放/暂停控制
const togglePlay = () => {
  if (!innerAudioContext.value) return
  
  if (isPlaying.value) {
    innerAudioContext.value.pause()
  } else {
    innerAudioContext.value.play()
  }
}

// 后退10秒
const seekBackward = () => {
  if (!innerAudioContext.value) return
  
  const newPosition = Math.max(0, currentTime.value - 10)
  innerAudioContext.value.seek(newPosition)
  currentTime.value = newPosition
}

// 前进10秒
const seekForward = () => {
  if (!innerAudioContext.value || duration.value <= 0) return
  
  const newPosition = Math.min(duration.value, currentTime.value + 10)
  innerAudioContext.value.seek(newPosition)
  currentTime.value = newPosition
}
</script>

<template>
  <view class="audio-player bg-gray-50 dark:bg-gray-800 rounded-xl p-4 mb-4">
    <view class="flex items-center justify-between mb-3">
      <view class="text-base font-medium text-gray-800 dark:text-gray-200">{{ title }}</view>
      <view class="text-xs text-gray-500 dark:text-gray-400">
        {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
      </view>
    </view>
    
    <!-- 进度条 -->
    <view class="progress-bar h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full mb-6 relative">
      <view 
        class="progress h-full bg-primary rounded-full" 
        :style="{ width: progress + '%' }"
      ></view>
    </view>
    
    <!-- 控制按钮 -->
    <view class="flex justify-center items-center">
      <view 
        class="w-10 h-10 rounded-full flex items-center justify-center cursor-pointer transition-colors"
        @click="seekBackward"
      >
        <text 
          class="text-gray-700 dark:text-gray-300 i-carbon-restart" 
          style="font-size: 24px;"
        ></text>
      </view>
      
      <view 
        class="w-12 h-12 rounded-full bg-primary flex items-center justify-center cursor-pointer transition-colors mx-6"
        @click="togglePlay"
      >
        <text 
          class="text-white" 
          :class="isPlaying ? 'i-carbon-pause-filled' : 'i-carbon-play-filled'"
          style="font-size: 24px;"
        ></text>
      </view>
      
      <view 
        class="w-10 h-10 rounded-full flex items-center justify-center cursor-pointer transition-colors"
        @click="seekForward"
      >
        <text 
          class="text-gray-700 dark:text-gray-300 i-carbon-restart rotate-180" 
          style="font-size: 24px;"
        ></text>
      </view>
    </view>
  </view>
</template>

<style scoped>
.progress-bar {
  transition: height 0.2s;
}
</style>