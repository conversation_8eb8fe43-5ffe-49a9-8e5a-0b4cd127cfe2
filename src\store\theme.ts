import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { defineStore } from 'pinia'

// 主题常量定义
export enum ThemeConstants {
  LIGHT = 'light',
  DARK = 'dark',
}

export const useThemeStore = defineStore(
  'theme-store',
  () => {
    /** 主题 */
    const theme = ref<ThemeConstants>(ThemeConstants.LIGHT)

    /** 主题变量 */
    const themeVars = ref<ConfigProviderThemeVars>({
      colorTheme: '#4d80f0', // 主题色
      // buttonPrimaryBgColor: '#07c160',
      // buttonPrimaryColor: '#07c160',
    })

    const isDark = computed(() => theme.value === ThemeConstants.DARK)

    const getSystemTheme = () => {
      // #ifdef MP-WEIXIN
      // 微信小程序使用 getAppBaseInfo
      const appBaseInfo = uni.getAppBaseInfo()
      if (appBaseInfo && appBaseInfo.theme) {
        // 确保返回的值是预期的枚举值
        return appBaseInfo.theme === ThemeConstants.DARK ? ThemeConstants.DARK : ThemeConstants.LIGHT
      }
      // #endif

      // #ifndef MP-WEIXIN
      // 其他平台使用 getSystemInfoSync
      const systemInfo = uni.getSystemInfoSync()
      if (systemInfo && systemInfo.theme) {
        // 确保返回的值是预期的枚举值
        return systemInfo.theme === ThemeConstants.DARK ? ThemeConstants.DARK : ThemeConstants.LIGHT
      }
      // #endif

      return ThemeConstants.LIGHT // 默认返回 light
    }

    /** 设置主题变量 */
    const setThemeVars = (partialVars: Partial<ConfigProviderThemeVars>) => {
      themeVars.value = { ...themeVars.value, ...partialVars }
    }
    /** 设置darkmode主题 */
    const setTheme = (mode: any) => {
      theme.value = mode
    }
    /** 切换主题 */
    const toggleTheme = () => {
      theme.value = theme.value === ThemeConstants.LIGHT ? ThemeConstants.DARK : ThemeConstants.LIGHT
    }

    /**
     * 初始化系统主题
     */
    const initSystemTheme = () => {
      const systemTheme = getSystemTheme()
      theme.value = systemTheme
      console.log('初始化系统主题:', theme.value)
    }

    return {
      /** 设置主题变量 */
      setThemeVars,
      /** 设置darkmode主题 */
      setTheme,
      /** 切换主题 */
      toggleTheme,
      /** 初始化系统主题 */
      initSystemTheme,
      /** 主题变量 */
      themeVars,
      /** 主题 */
      theme,
      isDark
    }
  },
  {
    persist: true,
  },
)
