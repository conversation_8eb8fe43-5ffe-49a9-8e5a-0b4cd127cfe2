<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "投诉详情"
    }
}</route>

<script lang="ts" setup>
import { complaintInfo } from '@/api/complaint'
onLoad((options) => {
    getInfo(options.id)
})

const InfoData = ref<any>({})
const getInfo = async (id: any) => {
    const res = await complaintInfo({
        id: id
    })
    if (res.code === 200) {
        InfoData.value = res.data
    }
    console.log('res: ', res)
}
</script>

<template>
    <view class="p-3">
        <view class="bg-white dark:bg-dark p-2 rounded mb-3">
            <view class="font-bold">{{ InfoData.scenicInfo?.scenicName }}</view>
            <view class="text-sm text-gray-500 my-2">{{ InfoData.createdAt }}</view>
            <wd-text :text="InfoData.complaintContent" custom-class="text-sm"></wd-text>
            <view class="flex flex-wrap gap-2 mt-2" v-if="InfoData.complaintImg">
                <wd-img :width="80" :height="80" radius="10px" v-for="item,index in InfoData.complaintImg.split(',')" :key="index"
                    :src="item" :enable-preview="true" mode="heightFix">
                    <template #error>
                        <view class="h-full w-full bg-gray-100 flex items-center justify-center text-sm">加载失败</view>
                    </template>
                </wd-img>
            </view>
        </view>
        <view class="bg-white dark:bg-[#1b1b1b] rounded p-2 mb-3">
            <view class="text-[16px] mb-4">投诉进度</view>
            <wd-steps :active="InfoData.complaintStatus == 1 ? 1 : 2" align-center>
                <wd-step :icon="'check-circle-filled'" title="用户提交" :description="InfoData.createdAt" />
                <wd-step :icon="InfoData.complaintStatus == 1 ? 'time' : 'check-circle-filled'"
                    :title="InfoData.complaintStatus == 1 ? '处理中' : '处理完成'" :description="InfoData.handlTime" />
            </wd-steps>
        </view>
        <view class="bg-white dark:bg-dark rounded p-2" v-if="InfoData.complaintStatus == 2">
            <view class="text-[16px] mb-2">处理结果</view>
            <wd-text :text="InfoData.feedback" custom-class="text-sm"></wd-text>
        </view>
    </view>
</template>
