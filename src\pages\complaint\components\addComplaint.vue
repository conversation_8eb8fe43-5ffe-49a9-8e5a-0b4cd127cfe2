<script lang="ts" setup>
import { createComplaint } from '@/api/complaint'
import { getScenicList } from '@/api/config'
import { useEnumStore, useTenantStore } from '@/store'
import cusUpload from '@/components/cusUpload/index.vue'
// 获取屏幕边界到安全区域距离

const enumsStore = useEnumStore()
const tenantStore = useTenantStore()

const formRef = ref(null)
let formData = reactive<any>({
    complaintImg: [],
    complaintContent: '',
    scenicId: null,
    complaintType: "",
    linkperson: '',
    linktel: '',
})
const formRules = {}

onLoad(() => {
    getScenic()
})

const scenicList = ref<any>([])
const getScenic = async () => {
    const res: any = await getScenicList({ tenantId: tenantStore.tenantInfo.tenantId })
    if (res.code === 200) {
        scenicList.value = res.data.map(item => {
            return { label: item.scenicName, value: item.id }
        })
    }
}

/**
 * 获取已上传的图片URL列表
 */
const getUploadedImageUrls = (): string[] => {
    return formData.complaintImg?.filter((file: any) => file.status === 'success' && file.response)
        .map((file: any) => file.response) || ''
}


const handleSubmit = async () => {
    // 获取上传成功的图片URL
    const imageUrls = getUploadedImageUrls()

    const submitData = {
        ...formData,
        complaintImg: imageUrls ? imageUrls.join(',') : ''// 使用图片URL数组
    }

    console.log('提交数据:', submitData)

    // 验证必填字段
    if (!submitData.scenicId) {
        uni.showToast({
            title: '请选择投诉景区',
            icon: 'none'
        })
        return
    }

    if (!submitData.complaintType) {
        uni.showToast({
            title: '请选择投诉类型',
            icon: 'none'
        })
        return
    }

    if (!submitData.complaintContent) {
        uni.showToast({
            title: '请填写投诉内容',
            icon: 'none'
        })
        return
    }

    // 调用API提交投诉
    const res = await createComplaint(submitData)
    if (res.code === 200) {
        uni.showToast({
            title: '投诉提交成功',
            icon: 'success'
        })
        // 清空表单数据
        Object.assign(formData, {
            complaintImg: [],
            complaintContent: '',
            scenicId: null,
            complaintType: "",
            linkperson: '',
            linktel: '',
        })

        formRef.value?.reset()
    } else {
        uni.showToast({
            title: '提交失败',
            icon: 'none'
        })
    }
    console.log('handleSubmit', formData)
}
</script>

<template>
    <view class="p-3">
        <wd-form ref="formRef" :model="formData" :rules="formRules">
            <wd-cell-group custom-class="rounded mb-3" :border="true">
                <wd-picker label="投诉景区" placeholder="请选择投诉景区" label-width="120rpx" prop="scenicId"
                    v-model="formData.scenicId" :columns="scenicList" />
                <wd-picker label="投诉类型" placeholder="请选择投诉类型" label-width="120rpx" prop="complaintType"
                    v-model="formData.complaintType" :columns="enumsStore.enumData?.complaintType?.list" />
                <wd-textarea label="投诉内容" label-width="120rpx" prop="complaintContent" rows="3"
                    v-model="formData.complaintContent" placeholder="请填写投诉内容" :maxlength="50" :show-word-limit="true" />
            </wd-cell-group>
            <wd-cell-group custom-class="rounded mb-3">
                <wd-cell title="上传照片（不超过9张）" prop="complaintImg" vertical>
                    <cusUpload v-model="formData.complaintImg" :show-limit-num="true" :limit="9"></cusUpload>
                </wd-cell>
            </wd-cell-group>
            <wd-cell-group custom-class="rounded" :border="true">
                <wd-input label="联系人" label-width="120rpx" prop="name" v-model="formData.linkperson"
                    placeholder="请输入联系人"></wd-input>
                <wd-input label="联系电话" label-width="120rpx" prop="name" type="number" v-model="formData.linktel"
                    placeholder="请输入联系电话"></wd-input>
            </wd-cell-group>
        </wd-form>
        <view class="p-3 pb-safe">
            <wd-button type="primary" block @click="handleSubmit">提交</wd-button>
        </view>
    </view>
</template>

<style lang="scss" scoped>
//</style>