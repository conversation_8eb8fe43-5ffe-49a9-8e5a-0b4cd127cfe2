<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "购票"
    }
}</route>

<script lang="ts" setup>
import { getScenicList } from '@/api/config';
import { useTenantStore } from '@/store';
const tenantStore = useTenantStore()

const scenicList = ref<any>([])
const getScenic = async () => {
    const res: any = await getScenicList({ tenantId: tenantStore.tenantInfo.tenantId })
    if (res.code === 200) {
        scenicList.value = res.data
    }
}
onLoad(() => {
    getScenic()
})
const goDetail = (item: any) => {
    uni.navigateTo({
        url: `/pages/ticket/detail?id=${item.id}`
    })
}
</script>

<template>
    <view class="p-3">
        <view class="bg-white dark:bg-dark p-2 rounded mb-3" v-for="item in scenicList" :key="item.id"
            @click="goDetail(item)">
            <view class="flex">
                <view class="w-[80px] h-[80px] flex-shrink-0">
                    <wd-img :src="item.scenicImage.split(',')[0]" width="100%" height="100%" mode="aspectFill"></wd-img>
                </view>
                <view class="ml-3 flex flex-col justify-between">
                    <view class="text-base">{{ item.scenicName }}</view>
                    <view>
                        <view class="text-xs text-gray-500">
                            <wd-icon name="time" size="22px"></wd-icon>
                            {{ item.businessHours }}
                        </view>
                        <view class="text-xs text-gray-500">
                            <wd-icon name="location" size="12" class="mr-1"></wd-icon>
                            {{ item.address }}
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<style lang="scss" scoped></style>
