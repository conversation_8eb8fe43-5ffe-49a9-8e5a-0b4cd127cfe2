<!-- 简化版二维码组件 - 专注于跨平台兼容性 -->
<script lang="ts" setup>
import QRCode from 'qrcode'
import { ref, watch, nextTick, computed, onMounted } from 'vue'

// Props 定义
interface Props {
  // 二维码内容
  value: string
  // 二维码大小
  size?: number
  // 错误纠正级别 L M Q H
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H'
  // 前景色
  color?: string
  // 背景色
  backgroundColor?: string
  // 边距
  margin?: number
  // 是否显示下载按钮
  showDownload?: boolean
  // 下载文件名
  downloadName?: string
  // 是否显示复制按钮
  showCopy?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  size: 200,
  errorCorrectionLevel: 'M',
  color: '#000000',
  backgroundColor: '#ffffff',
  margin: 4,
  showDownload: false,
  downloadName: 'qrcode',
  showCopy: false
})

// Emits 定义
const emit = defineEmits<{
  generated: [dataUrl: string]
  error: [error: Error]
  downloaded: [filename: string]
  copied: []
}>()

// 响应式数据
const qrDataUrl = ref('')
const isGenerating = ref(false)
const errorMessage = ref('')

// QR码生成选项
const qrOptions = computed(() => ({
  errorCorrectionLevel: props.errorCorrectionLevel,
  color: {
    dark: props.color,
    light: props.backgroundColor
  },
  margin: props.margin,
  width: props.size
}))

// 生成二维码 - 使用最兼容的DataURL方式
const generateQRCode = async () => {
  if (!props.value.trim()) {
    errorMessage.value = '请输入要生成二维码的内容'
    return
  }

  isGenerating.value = true
  errorMessage.value = ''

  try {
    // 使用DataURL方式，所有平台都支持
    const dataUrl = await QRCode.toDataURL(props.value, qrOptions.value)
    qrDataUrl.value = dataUrl
    emit('generated', dataUrl)
  } catch (error) {
    const err = error as Error
    console.error('QR Code generation error:', err)
    errorMessage.value = `生成失败: ${err.message}`
    emit('error', err)
  } finally {
    isGenerating.value = false
  }
}

// 下载二维码
const downloadQRCode = () => {
  if (!qrDataUrl.value) {
    uni.showToast({
      title: '请先生成二维码',
      icon: 'none'
    })
    return
  }
  
  // 在H5环境下使用浏览器下载
  // #ifdef H5
  const link = document.createElement('a')
  link.download = `${props.downloadName}.png`
  link.href = qrDataUrl.value
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  // #endif
  
  // 在小程序和App环境下保存到相册
  // #ifndef H5
  uni.saveImageToPhotosAlbum({
    filePath: qrDataUrl.value,
    success: () => {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  })
  // #endif
  
  emit('downloaded', `${props.downloadName}.png`)
}

// 复制二维码内容
const copyContent = () => {
  // #ifdef H5
  if (navigator.clipboard) {
    navigator.clipboard.writeText(props.value).then(() => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
      emit('copied')
    }).catch(() => {
      uni.showToast({
        title: '复制失败',
        icon: 'none'
      })
    })
  }
  // #endif
  
  // #ifndef H5
  uni.setClipboardData({
    data: props.value,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
      emit('copied')
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none'
      })
    }
  })
  // #endif
}

// 监听props变化，自动重新生成
watch(() => [props.value, props.size, props.errorCorrectionLevel, props.color, props.backgroundColor, props.margin], () => {
  if (props.value) {
    nextTick(() => {
      generateQRCode()
    })
  }
}, { deep: true })

// 组件挂载后生成二维码
onMounted(() => {
  if (props.value) {
    nextTick(() => {
      generateQRCode()
    })
  }
})

// 暴露方法给父组件
defineExpose({
  generateQRCode,
  downloadQRCode,
  copyContent
})
</script>

<template>
  <view class="simple-qr-container" :style="{ width: `${size}px`, height: `${size}px` }">
    <!-- 错误提示 -->
    <view v-if="errorMessage" class="error-message">
      <text class="error-text">{{ errorMessage }}</text>
    </view>
    
    <!-- 加载状态 -->
    <view v-if="isGenerating" class="loading-container">
      <text class="loading-text">生成中...</text>
    </view>
    
    <!-- 二维码图片 -->
    <image 
      v-if="qrDataUrl && !isGenerating"
      :src="qrDataUrl"
      class="qr-image"
      mode="aspectFit"
      :style="{ width: '100%', height: '100%' }"
    />
    
    <!-- 操作按钮 -->
    <view v-if="(showDownload || showCopy) && qrDataUrl && !isGenerating" class="action-buttons">
      <button 
        v-if="showDownload" 
        @click="downloadQRCode"
        class="action-btn download-btn"
        size="mini"
      >
        下载
      </button>
      
      <button 
        v-if="showCopy" 
        @click="copyContent"
        class="action-btn copy-btn"
        size="mini"
      >
        复制
      </button>
    </view>
  </view>
</template>

<style scoped>
.simple-qr-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 0, 0, 0.1);
  border: 1px solid #ff4444;
  border-radius: 4px;
  padding: 8px 12px;
  z-index: 10;
}

.error-text {
  color: #ff4444;
  font-size: 12px;
}

.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  padding: 8px 12px;
  z-index: 10;
}

.loading-text {
  color: #ffffff;
  font-size: 12px;
}

.qr-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.action-buttons {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  z-index: 5;
}

.action-btn {
  background-color: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.action-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

.download-btn:active {
  background-color: #007aff;
}

.copy-btn:active {
  background-color: #34c759;
}
</style>
