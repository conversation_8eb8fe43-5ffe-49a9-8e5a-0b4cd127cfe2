<route lang="jsonc">{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "二维码简单测试"
  }
}</route>

<script lang="ts" setup>
import { ref } from 'vue'
import CusQrCode from '@/components/cusQrCode/index.vue'

const testValue = ref('https://unibest.tech')

const onGenerated = (dataUrl: string) => {
  console.log('QR Code generated successfully')
  uni.showToast({
    title: '生成成功',
    icon: 'success'
  })
}

const onError = (error: Error) => {
  console.error('QR Code error:', error)
  uni.showToast({
    title: `生成失败: ${error.message}`,
    icon: 'none'
  })
}
</script>

<template>
  <view class="container">
    <view class="header">
      <text class="title">二维码组件测试</text>
      <text class="subtitle">测试 canvas-id 修复</text>
    </view>

    <view class="test-section">
      <view class="section-title">基础测试</view>
      
      <view class="qr-container">
        <CusQrCode
          :value="testValue"
          :size="200"
          format="canvas"
          @generated="onGenerated"
          @error="onError"
        />
      </view>
      
      <view class="input-container">
        <input 
          v-model="testValue" 
          placeholder="输入二维码内容"
          class="input"
        />
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">不同格式测试</view>
      
      <view class="format-grid">
        <view class="format-item">
          <text class="format-label">Canvas</text>
          <view class="qr-wrapper">
            <CusQrCode
              value="Canvas格式测试"
              :size="120"
              format="canvas"
              @error="onError"
            />
          </view>
        </view>
        
        <view class="format-item">
          <text class="format-label">Image</text>
          <view class="qr-wrapper">
            <CusQrCode
              value="Image格式测试"
              :size="120"
              format="image"
              @error="onError"
            />
          </view>
        </view>
        
        <!-- H5 平台才显示 SVG -->
        <!-- #ifdef H5 -->
        <view class="format-item">
          <text class="format-label">SVG</text>
          <view class="qr-wrapper">
            <CusQrCode
              value="SVG格式测试"
              :size="120"
              format="svg"
              @error="onError"
            />
          </view>
        </view>
        <!-- #endif -->
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">平台信息</view>
      <view class="platform-info">
        <text class="info-text">当前平台: {{ platformInfo }}</text>
        <text class="info-text">推荐格式: {{ recommendedFormat }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
export default {
  computed: {
    platformInfo() {
      // #ifdef H5
      return 'H5 浏览器'
      // #endif
      // #ifdef MP-WEIXIN
      return '微信小程序'
      // #endif
      // #ifdef MP-ALIPAY
      return '支付宝小程序'
      // #endif
      // #ifdef APP-PLUS
      return 'App'
      // #endif
      return '未知平台'
    },
    recommendedFormat() {
      // #ifdef H5
      return 'canvas/svg/image 都支持'
      // #endif
      // #ifdef MP
      return 'canvas/image (推荐 image)'
      // #endif
      // #ifdef APP-PLUS
      return 'canvas/image'
      // #endif
      return 'canvas'
    }
  }
}
</script>

<style scoped>
.container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 24px;
}

.title {
  display: block;
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
}

.subtitle {
  display: block;
  font-size: 14px;
  color: #666666;
}

.test-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  border-left: 4px solid #1890ff;
  padding-left: 8px;
}

.qr-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  margin-bottom: 16px;
  min-height: 240px;
}

.input-container {
  margin-top: 16px;
}

.input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  background-color: #ffffff;
}

.format-grid {
  display: flex;
  gap: 16px;
  justify-content: space-around;
  flex-wrap: wrap;
}

.format-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 140px;
}

.format-label {
  font-size: 12px;
  color: #666666;
  font-weight: 600;
}

.qr-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
  min-height: 140px;
  width: 100%;
}

.platform-info {
  background-color: #f0f8ff;
  border: 1px solid #d6e4ff;
  border-radius: 6px;
  padding: 12px;
}

.info-text {
  display: block;
  font-size: 14px;
  color: #1890ff;
  margin-bottom: 4px;
}

.info-text:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 8px;
  }
  
  .format-grid {
    flex-direction: column;
    align-items: center;
  }
  
  .format-item {
    width: 100%;
    max-width: 200px;
  }
}
</style>
