<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "订单详情"
    }
}</route>

<script lang="ts" setup>
import { getTicketOrderInfo } from '@/api/ticket'
import { useEnumStore } from '@/store'
const enumStore = useEnumStore()
onLoad((options) => {
    getOrderData(options.id)
})
const orderData = ref<any>({})
const orderTicketList = ref<any>([])
const getOrderData = async (id: any) => {
    const res: any = await getTicketOrderInfo({
        orderId: id,
    })
    if (res.code === 200) {
        orderData.value = res.data
        orderTicketList.value = res.data.orderItemList
        console.log(res.data)
    }
}

const filterStatus = (status: any) => {
    let obj = { label: '', color: 'default' }
    obj.label = enumStore.enumData?.ticketOrderStatus.list.find(item => item.value === status)?.label
    if ([1].includes(status)) {
        obj.color = 'text-warning'
    } else if ([2, 3, 4, 9].includes(status)) {
        obj.color = 'text-success'
    } else if ([8].includes(status)) {
        obj.color = 'text-danger'
    } else {
        obj.color = 'text-info'
    }
    return obj
}
</script>

<template>
    <view class="p-3">
        <view class="bg-white dark:bg-dark rounded p-2">
            <view class="text-sm flex justify-between items-center">
                <view class="">票类信息</view>
                <view :class="`${filterStatus(orderData.orderStatus).color}`">{{
                    filterStatus(orderData.orderStatus).label }}
                </view>
            </view>
            <wd-divider custom-class="!my-2 !p-0" color="rgba(0,0,0,0.1)"></wd-divider>
        </view>
    </view>
</template>

<style lang="scss" scoped></style>
