<route lang="jsonc" type="page">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "编辑用户信息"
    }
}</route>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store'
import { useColPickerData } from '@/hooks/useColPickerData'
import { customOssUpload } from '@/hooks/aliyun-oss'
const { colPickerData, findChildrenByCode } = useColPickerData()
import dayjs from 'dayjs'
import { updateUserInfo, getWechatPhoneNumber, getUserInfo } from '@/api/user'

const userStore = useUserStore()
// 使用storeToRefs解构userInfo
const { userInfo } = storeToRefs(userStore)

const formData = ref<any>({
    avatar: userInfo.value.avatar,
    nickname: userInfo.value.nickname,
    name: userInfo.value.name,
    birthday: userInfo.value.birthday ? new Date(userInfo.value.birthday).getTime() : new Date().getTime(),
    phone: userInfo.value.phone,
    idcard: userInfo.value.idcard,
    sex: userInfo.value.sex,
    regionProvince: userInfo.value.regionProvince,
    regionProvinceCode: userInfo.value.regionProvinceCode,
    regionCity: userInfo.value.regionCity,
    regionCityCode: userInfo.value.regionCityCode,
    regionCounty: userInfo.value.regionCounty,
    regionCountyCode: userInfo.value.regionCountyCode,
    region: userInfo.value.regionCountyCode ? [userInfo.value.regionProvinceCode, userInfo.value.regionCityCode, userInfo.value.regionCountyCode] : [],
})
const sexMap = [{ label: '保密', value: 0 }, { label: '男', value: 1 }, { label: '女', value: 2 }]
const minDate = ref(new Date(1910, 0, 1).getTime()) // 最小日期为1900年1月1日,需要数字时间戳
const maxDate = ref(new Date().getTime())
// 微信小程序下选择头像事件
const onChooseAvatar = async (e: any) => {
    console.log('选择头像', e)
    const { avatarUrl } = e.detail
    let res = await customOssUpload({
        filePath: avatarUrl,
        fileTypeTag: 'avatar',
    });
    if (res) {
        formData.value.avatar = res
        useUserStore().setUserAvatar(res)
        uni.showToast({
            title: '上传成功',
            icon: 'success',
            duration: 2000,
        })
    }
    console.log('上传成功', res)
}
// 微信小程序下获取手机号
const getNumber = async (e: any) => {
    const res: any = await getWechatPhoneNumber({ code: e.code })
    console.log('获取手机号', res)
    formData.value.phone = res.data.phoneNumber
}

// 身份证
const idcardBlur = (val: any) => {
    if (val.value && val.value.length == 18) {

        const { birthday, sex } = getBirthdayAndSex(val.value)
        formData.value.birthday = new Date(birthday).getTime()
        formData.value.sex = sex
    }
}
// 根据身份证获取生日和性别
const getBirthdayAndSex = (idcard: string) => {
    const birthday = idcard.substring(6, 10) + '-' + idcard.substring(10, 12) + '-' + idcard.substring(12, 14)
    const sex = Number(idcard.substring(16, 17)) % 2 == 1 ? '1' : '2'
    return { birthday, sex }
}

// 所在地区
const getAreaData = () => {
    const result = [
        colPickerData.map((item) => {
            return {
                value: item.value,
                label: item.text
            }
        })
    ]

    if (formData.value.region && formData.value.region.length > 0) {
        const children1 = findChildrenByCode(colPickerData, formData.value.region[0])
        if (children1) {
            result.push(
                children1.map((item) => {
                    return {
                        value: item.value,
                        label: item.text
                    }
                })
            )

            const children2 = findChildrenByCode(colPickerData, formData.value.region[1])
            if (children2) {
                result.push(
                    children2.map((item) => {
                        return {
                            value: item.value,
                            label: item.text
                        }
                    })
                )
            }
        }
    }

    return result
}

const area = ref<any[]>(getAreaData())
const areaChange = ({ selectedItem, resolve, finish }) => {
    console.log('areaChange', formData)
    const areaData = findChildrenByCode(colPickerData, selectedItem.value)
    if (areaData && areaData.length) {
        resolve(
            areaData.map((item) => {
                return {
                    value: item.value,
                    label: item.text
                }
            })
        )
    } else {
        finish()
    }
}

const handleSubmit = async () => {
    let submitParams = {
        ...formData.value,
        birthday: dayjs(formData.value.birthday).format('YYYY-MM-DD')
    }
    const res: any = await updateUserInfo(submitParams)
    if (res.code === 200) {
        uni.showToast({
            title: '修改成功'
        })
        setTimeout(() => {
            useUserStore().setUserInfo(res.data)
            uni.navigateBack()
        }, 1000)
    } else {
        uni.showToast({
            title: res.message,
            icon: 'error'
        })
    }
    console.log('handleSubmit', submitParams);
}
// 地区选择
const areaConfirm = ({ selectedItems, value }) => {
    console.log('areaConfirm', selectedItems, value);
    // formData.region = selectedItems.map((item) => item.label).join('')
    formData.value.regionProvince = selectedItems[0].label
    formData.value.regionProvinceCode = value[0]
    formData.value.regionCity = selectedItems[1].label
    formData.value.regionCityCode = value[1]
    formData.value.regionCounty = selectedItems[2].label
    formData.value.regionCountyCode = value[2]
}
</script>

<template>
    <view class="p-3">
        <view class="bg-white dark:bg-[#1b1b1b] py-2 rounded mb-5">
            <button class="avatar-button" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
                <image :src="formData.avatar" mode="scaleToFill" round class="h-full w-full" />
            </button>
            <view>
                <wd-form label-position="top" ref="formRef" :model="formData">
                    <wd-cell-group custom-class="group" title="" :border="true">
                        <wd-input label="昵称" label-width="120rpx" placeholder="昵称" prop="nickname" clearable
                            type="nickname" v-model="formData.nickname"></wd-input>

                        <wd-input label="手机号" label-width="120rpx" placeholder="请输入手机号" type="number" prop="phone"
                            v-model="formData.phone" :center="true">
                            <template #suffix>
                                <wd-button type="text" size="small" open-type="getPhoneNumber"
                                    @getphonenumber="getNumber">一键获取</wd-button>
                            </template>
                        </wd-input>
                        <wd-input label="身份证号" label-width="120rpx" prop="idcard" type="idcard" clearable
                            v-model="formData.idcard" placeholder="请输入身份证号" @blur="idcardBlur" />
                        <wd-input label="真实姓名" label-width="120rpx" prop="name" clearable v-model="formData.name"
                            placeholder="请输入真实姓名" />
                        <wd-picker label="性别" label-width="120rpx" placeholder="请选择性别" prop="promotion"
                            v-model="formData.sex" :columns="sexMap" />
                        <wd-datetime-picker type="date" label="出生日期" label-width="120rpx" :minDate="minDate"
                            :maxDate="maxDate" placeholder="请选择出生日期" prop="birthday" v-model="formData.birthday"
                            :default-value="formData.birthday" />
                        <wd-col-picker label="所在地区" placeholder="请选择所在地区" label-width="120rpx" prop="region"
                            v-model="formData.region" :columns="area" auto-complete :column-change="areaChange"
                            @confirm="areaConfirm" />
                    </wd-cell-group>
                </wd-form>
            </view>
        </view>
        <view class="text-center px-5">
            <wd-button type="primary" size="large" block @click="handleSubmit">保存</wd-button>
        </view>
    </view>
</template>

<style lang="scss" scoped>
.avatar-button {
    padding: 0;
    width: 90px;
    height: 90px;
    overflow: hidden;
    border-radius: 50%;
    margin-bottom: 15rpx;
}
</style>
