<template>
  <view class="examples-container">
    <view class="example-section">
      <view class="example-title">基础用法</view>
      <view class="example-content">
        <CusQrCode 
          value="https://unibest.tech" 
          :size="150"
        />
      </view>
      <view class="example-code">
        <text>&lt;CusQrCode value="https://unibest.tech" :size="150" /&gt;</text>
      </view>
    </view>

    <view class="example-section">
      <view class="example-title">自定义颜色</view>
      <view class="example-content">
        <CusQrCode 
          value="自定义颜色的二维码" 
          :size="150"
          color="#1890ff"
          background-color="#f0f8ff"
        />
      </view>
      <view class="example-code">
        <text>&lt;CusQrCode value="自定义颜色的二维码" color="#1890ff" background-color="#f0f8ff" /&gt;</text>
      </view>
    </view>

    <view class="example-section">
      <view class="example-title">带Logo</view>
      <view class="example-content">
        <CusQrCode 
          value="https://unibest.tech" 
          :size="150"
          logo="https://unibest.tech/logo.png"
          :logo-size="0.25"
        />
      </view>
      <view class="example-code">
        <text>&lt;CusQrCode value="https://unibest.tech" logo="logo.png" :logo-size="0.25" /&gt;</text>
      </view>
    </view>

    <view class="example-section">
      <view class="example-title">带操作按钮</view>
      <view class="example-content">
        <CusQrCode 
          value="可下载和复制的二维码" 
          :size="150"
          :show-download="true"
          :show-copy="true"
          download-name="my-qrcode"
        />
      </view>
      <view class="example-code">
        <text>&lt;CusQrCode value="内容" :show-download="true" :show-copy="true" /&gt;</text>
      </view>
    </view>

    <view class="example-section">
      <view class="example-title">高错误纠正级别</view>
      <view class="example-content">
        <CusQrCode 
          value="高容错率二维码，即使部分损坏也能识别" 
          :size="150"
          error-correction-level="H"
          :margin="2"
        />
      </view>
      <view class="example-code">
        <text>&lt;CusQrCode value="内容" error-correction-level="H" /&gt;</text>
      </view>
    </view>

    <view class="example-section">
      <view class="example-title">响应式布局</view>
      <view class="example-content responsive-demo">
        <CusQrCode 
          value="响应式二维码，会根据容器大小自适应" 
          :size="200"
          :responsive="true"
        />
      </view>
      <view class="example-code">
        <text>&lt;CusQrCode value="内容" :responsive="true" /&gt;</text>
      </view>
    </view>

    <view class="example-section">
      <view class="example-title">WiFi连接信息</view>
      <view class="example-content">
        <CusQrCode 
          value="WIFI:T:WPA;S:MyWiFi;P:password123;;"
          :size="150"
          color="#52c41a"
          background-color="#f6ffed"
        />
      </view>
      <view class="example-code">
        <text>WiFi格式: WIFI:T:WPA;S:网络名;P:密码;;</text>
      </view>
    </view>

    <view class="example-section">
      <view class="example-title">联系人信息</view>
      <view class="example-content">
        <CusQrCode 
          :value="vcardData"
          :size="150"
          color="#722ed1"
          background-color="#f9f0ff"
        />
      </view>
      <view class="example-code">
        <text>vCard格式的电子名片</text>
      </view>
    </view>

    <view class="example-section">
      <view class="example-title">SVG格式</view>
      <view class="example-content">
        <CusQrCode 
          value="SVG格式的二维码，矢量图形" 
          :size="150"
          format="svg"
        />
      </view>
      <view class="example-code">
        <text>&lt;CusQrCode value="内容" format="svg" /&gt;</text>
      </view>
    </view>

    <view class="example-section">
      <view class="example-title">编程式调用</view>
      <view class="example-content">
        <CusQrCode 
          ref="qrRef"
          :value="dynamicValue"
          :size="150"
          :show-download="true"
        />
        <view class="control-buttons">
          <button @click="changeContent" size="mini">更换内容</button>
          <button @click="downloadQR" size="mini">下载</button>
          <button @click="regenerate" size="mini">重新生成</button>
        </view>
      </view>
      <view class="example-code">
        <text>通过ref调用组件方法</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import CusQrCode from '@/components/cusQrCode/index.vue'

// 动态内容
const dynamicValue = ref('初始内容')
const qrRef = ref()

// vCard数据
const vcardData = `BEGIN:VCARD
VERSION:3.0
FN:张三
ORG:示例公司
TEL:+86-138-0013-8000
EMAIL:<EMAIL>
URL:https://example.com
END:VCARD`

// 内容列表
const contentList = [
  '初始内容',
  'https://unibest.tech',
  '这是一个测试二维码',
  '扫码关注我们',
  '联系电话：138-0013-8000'
]
let currentIndex = 0

// 更换内容
const changeContent = () => {
  currentIndex = (currentIndex + 1) % contentList.length
  dynamicValue.value = contentList[currentIndex]
}

// 下载二维码
const downloadQR = () => {
  if (qrRef.value) {
    qrRef.value.downloadQRCode()
  }
}

// 重新生成
const regenerate = () => {
  if (qrRef.value) {
    qrRef.value.generateQRCode()
  }
}
</script>

<style scoped>
.examples-container {
  padding: 16px;
  background-color: #f5f5f5;
}

.example-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.example-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12px;
  border-left: 4px solid #1890ff;
  padding-left: 8px;
}

.example-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  margin-bottom: 8px;
  min-height: 180px;
}

.responsive-demo {
  width: 60%;
  margin: 0 auto;
}

.example-code {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 8px 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #586069;
  overflow-x: auto;
}

.control-buttons {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  justify-content: center;
}

.control-buttons button {
  background-color: #1890ff;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
}

.control-buttons button:active {
  background-color: #096dd9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .examples-container {
    padding: 8px;
  }
  
  .example-section {
    padding: 12px;
    margin-bottom: 12px;
  }
  
  .example-content {
    min-height: 150px;
    padding: 12px;
  }
  
  .responsive-demo {
    width: 80%;
  }
  
  .control-buttons {
    flex-wrap: wrap;
  }
}
</style>
