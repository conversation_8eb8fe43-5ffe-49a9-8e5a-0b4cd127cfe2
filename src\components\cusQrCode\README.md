# CusQrCode 二维码组件

一个功能丰富的 Vue 3 + UniApp 二维码生成组件，基于 `qrcode` 库封装。

## ✨ 特性

- 🎨 **多种输出格式**: 支持 Canvas、SVG、Image 三种格式
- 🎯 **高度可定制**: 支持自定义颜色、大小、边距等
- 🖼️ **Logo 支持**: 可在二维码中心添加 Logo 图片
- 📱 **响应式设计**: 支持自适应容器大小
- 💾 **下载功能**: 一键下载生成的二维码
- 📋 **复制功能**: 快速复制二维码内容
- 🔧 **错误纠正**: 支持 L/M/Q/H 四种错误纠正级别
- 🌐 **跨平台**: 支持 H5、小程序、App 等多个平台
- 📦 **TypeScript**: 完整的 TypeScript 类型支持

## 📦 安装

组件依赖 `qrcode` 库，请确保已安装：

```bash
pnpm add qrcode
pnpm add -D @types/qrcode  # TypeScript 支持
```

## 🚀 基础用法

```vue
<template>
  <CusQrCode value="https://unibest.tech" :size="200" />
</template>

<script setup>
import CusQrCode from '@/components/cusQrCode/index.vue'
</script>
```

## 📋 Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `value` | `string` | `''` | 二维码内容 |
| `size` | `number` | `200` | 二维码大小（像素） |
| `errorCorrectionLevel` | `'L' \| 'M' \| 'Q' \| 'H'` | `'M'` | 错误纠正级别 |
| `color` | `string` | `'#000000'` | 前景色 |
| `backgroundColor` | `string` | `'#ffffff'` | 背景色 |
| `margin` | `number` | `4` | 边距 |
| `showDownload` | `boolean` | `false` | 是否显示下载按钮 |
| `downloadName` | `string` | `'qrcode'` | 下载文件名 |
| `showCopy` | `boolean` | `false` | 是否显示复制按钮 |
| `logo` | `string` | `''` | Logo 图片地址 |
| `logoSize` | `number` | `0.2` | Logo 大小比例（0-1） |
| `format` | `'canvas' \| 'svg' \| 'image'` | `'canvas'` | 输出格式 |
| `responsive` | `boolean` | `false` | 是否自适应容器 |

## 🎯 Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `generated` | `dataUrl: string` | 二维码生成成功 |
| `error` | `error: Error` | 二维码生成失败 |
| `downloaded` | `filename: string` | 下载完成 |
| `copied` | - | 内容复制完成 |

## 🔧 Methods

通过 `ref` 可以调用以下方法：

| 方法名 | 说明 |
|--------|------|
| `generateQRCode()` | 重新生成二维码 |
| `downloadQRCode()` | 下载二维码 |
| `copyContent()` | 复制二维码内容 |

## 🎨 使用示例

### 自定义颜色

```vue
<CusQrCode 
  value="自定义颜色" 
  color="#1890ff" 
  background-color="#f0f8ff" 
/>
```

### 添加 Logo

```vue
<CusQrCode 
  value="https://unibest.tech" 
  logo="/static/logo.png"
  :logo-size="0.25"
/>
```

### 带操作按钮

```vue
<CusQrCode 
  value="可下载的二维码" 
  :show-download="true"
  :show-copy="true"
  download-name="my-qrcode"
/>
```

### 高错误纠正级别

```vue
<CusQrCode 
  value="高容错率二维码" 
  error-correction-level="H"
/>
```

### 响应式布局

```vue
<view style="width: 50%;">
  <CusQrCode 
    value="响应式二维码" 
    :responsive="true"
  />
</view>
```

### WiFi 连接信息

```vue
<CusQrCode 
  value="WIFI:T:WPA;S:MyWiFi;P:password123;;"
  color="#52c41a"
/>
```

### 联系人信息（vCard）

```vue
<CusQrCode 
  :value="vcardData"
  color="#722ed1"
/>

<script setup>
const vcardData = `BEGIN:VCARD
VERSION:3.0
FN:张三
TEL:+86-138-0013-8000
EMAIL:<EMAIL>
END:VCARD`
</script>
```

### 编程式调用

```vue
<template>
  <CusQrCode 
    ref="qrRef"
    :value="content"
    :show-download="true"
  />
  <button @click="regenerate">重新生成</button>
  <button @click="download">下载</button>
</template>

<script setup>
import { ref } from 'vue'

const qrRef = ref()
const content = ref('动态内容')

const regenerate = () => {
  qrRef.value?.generateQRCode()
}

const download = () => {
  qrRef.value?.downloadQRCode()
}
</script>
```

## 🔍 错误纠正级别说明

| 级别 | 容错率 | 适用场景 |
|------|--------|----------|
| L | ~7% | 清洁环境，打印质量好 |
| M | ~15% | 一般环境（默认） |
| Q | ~25% | 可能有污损的环境 |
| H | ~30% | 恶劣环境，高容错需求 |

## 🌐 平台兼容性

| 平台 | Canvas | SVG | Image | 下载 | 复制 |
|------|--------|-----|-------|------|------|
| H5 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 微信小程序 | ✅ | ❌ | ✅ | 保存到相册 | ✅ |
| App | ✅ | ❌ | ✅ | 保存到相册 | ✅ |

## 📝 注意事项

1. **Logo 图片**: 确保 Logo 图片支持跨域访问
2. **内容长度**: 二维码内容过长会影响识别率，建议控制在合理范围内
3. **颜色对比**: 确保前景色和背景色有足够的对比度
4. **错误纠正**: 添加 Logo 时建议使用较高的错误纠正级别
5. **平台差异**: 不同平台的下载和复制功能实现方式不同

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
