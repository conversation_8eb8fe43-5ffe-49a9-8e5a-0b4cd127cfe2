<route lang="jsonc">{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "简化版二维码测试"
  }
}</route>

<script lang="ts" setup>
import { ref } from 'vue'
import SimpleQrCode from '@/components/cusQrCode/SimpleQrCode.vue'

const testValue = ref('https://unibest.tech')
const qrSize = ref(200)

const onGenerated = (dataUrl: string) => {
  console.log('QR Code generated successfully')
  uni.showToast({
    title: '生成成功',
    icon: 'success'
  })
}

const onError = (error: Error) => {
  console.error('QR Code error:', error)
  uni.showToast({
    title: `生成失败: ${error.message}`,
    icon: 'none'
  })
}

const onDownloaded = (filename: string) => {
  console.log('Downloaded:', filename)
}

const onCopied = () => {
  console.log('Content copied')
}

// 预设示例
const examples = [
  'https://unibest.tech',
  '这是一个测试二维码',
  'WIFI:T:WPA;S:MyWiFi;P:password123;;',
  'tel:+86-138-0013-8000',
  'mailto:<EMAIL>'
]

const applyExample = (example: string) => {
  testValue.value = example
}
</script>

<template>
  <view class="container">
    <view class="header">
      <text class="title">简化版二维码组件</text>
      <text class="subtitle">解决 Canvas 兼容性问题</text>
    </view>

    <!-- 主要测试区域 -->
    <view class="test-section">
      <view class="section-title">基础测试</view>
      
      <view class="qr-container">
        <SimpleQrCode
          :value="testValue"
          :size="qrSize"
          :show-download="true"
          :show-copy="true"
          @generated="onGenerated"
          @error="onError"
          @downloaded="onDownloaded"
          @copied="onCopied"
        />
      </view>
      
      <view class="controls">
        <view class="input-group">
          <text class="label">二维码内容:</text>
          <textarea 
            v-model="testValue" 
            placeholder="输入二维码内容"
            class="textarea"
            maxlength="500"
          />
        </view>
        
        <view class="input-group">
          <text class="label">尺寸: {{ qrSize }}px</text>
          <slider 
            :value="qrSize"
            @change="(e) => qrSize = e.detail.value"
            :min="100" 
            :max="300" 
            :step="10"
            class="slider"
          />
        </view>
      </view>
    </view>

    <!-- 示例模板 -->
    <view class="test-section">
      <view class="section-title">示例模板</view>
      <view class="examples">
        <button 
          v-for="(example, index) in examples" 
          :key="index"
          @click="applyExample(example)"
          class="example-btn"
          size="mini"
        >
          {{ example.length > 30 ? example.substring(0, 30) + '...' : example }}
        </button>
      </view>
    </view>

    <!-- 多尺寸测试 -->
    <view class="test-section">
      <view class="section-title">多尺寸测试</view>
      <view class="size-grid">
        <view class="size-item">
          <text class="size-label">小 (100px)</text>
          <SimpleQrCode
            value="小尺寸测试"
            :size="100"
            @error="onError"
          />
        </view>
        
        <view class="size-item">
          <text class="size-label">中 (150px)</text>
          <SimpleQrCode
            value="中尺寸测试"
            :size="150"
            @error="onError"
          />
        </view>
        
        <view class="size-item">
          <text class="size-label">大 (200px)</text>
          <SimpleQrCode
            value="大尺寸测试"
            :size="200"
            @error="onError"
          />
        </view>
      </view>
    </view>

    <!-- 颜色测试 -->
    <view class="test-section">
      <view class="section-title">颜色测试</view>
      <view class="color-grid">
        <view class="color-item">
          <text class="color-label">经典黑白</text>
          <SimpleQrCode
            value="经典黑白"
            :size="120"
            color="#000000"
            background-color="#ffffff"
            @error="onError"
          />
        </view>
        
        <view class="color-item">
          <text class="color-label">蓝色主题</text>
          <SimpleQrCode
            value="蓝色主题"
            :size="120"
            color="#1890ff"
            background-color="#f0f8ff"
            @error="onError"
          />
        </view>
        
        <view class="color-item">
          <text class="color-label">绿色主题</text>
          <SimpleQrCode
            value="绿色主题"
            :size="120"
            color="#52c41a"
            background-color="#f6ffed"
            @error="onError"
          />
        </view>
      </view>
    </view>

    <!-- 平台信息 -->
    <view class="test-section">
      <view class="section-title">平台信息</view>
      <view class="platform-info">
        <text class="info-text">当前平台: {{ platformInfo }}</text>
        <text class="info-text">组件版本: 简化版 (Image Only)</text>
        <text class="info-text">兼容性: 全平台支持</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
export default {
  computed: {
    platformInfo() {
      // #ifdef H5
      return 'H5 浏览器'
      // #endif
      // #ifdef MP-WEIXIN
      return '微信小程序'
      // #endif
      // #ifdef MP-ALIPAY
      return '支付宝小程序'
      // #endif
      // #ifdef APP-PLUS
      return 'App'
      // #endif
      return '未知平台'
    }
  }
}
</script>

<style scoped>
.container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 24px;
}

.title {
  display: block;
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
}

.subtitle {
  display: block;
  font-size: 14px;
  color: #52c41a;
  font-weight: 600;
}

.test-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  border-left: 4px solid #52c41a;
  padding-left: 8px;
}

.qr-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.textarea {
  width: 100%;
  min-height: 60px;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  background-color: #ffffff;
}

.slider {
  width: 100%;
}

.examples {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-btn {
  background-color: #f0f8ff;
  color: #1890ff;
  border: 1px solid #d6e4ff;
  border-radius: 4px;
  padding: 8px 12px;
  text-align: left;
  font-size: 12px;
}

.example-btn:active {
  background-color: #e6f7ff;
}

.size-grid,
.color-grid {
  display: flex;
  gap: 16px;
  justify-content: space-around;
  flex-wrap: wrap;
}

.size-item,
.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.size-label,
.color-label {
  font-size: 12px;
  color: #666666;
  font-weight: 600;
}

.platform-info {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 12px;
}

.info-text {
  display: block;
  font-size: 14px;
  color: #52c41a;
  margin-bottom: 4px;
}

.info-text:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 8px;
  }
  
  .size-grid,
  .color-grid {
    flex-direction: column;
    align-items: center;
  }
  
  .size-item,
  .color-item {
    width: 100%;
    max-width: 200px;
  }
}
</style>
