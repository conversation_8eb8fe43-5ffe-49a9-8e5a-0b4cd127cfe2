<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "门票列表"
    }
}</route>

<script lang="ts" setup>
import { getRecommendList } from '@/api/ticket';
import { useTenantStore } from '@/store';
const tenantStore = useTenantStore();
const recommendList = ref<any>([]);
onLoad(() => {
    getRecommend()
})
const getRecommend = async () => {
    const res = await getRecommendList({
        tenantId: tenantStore.tenantInfo.tenantId
    })
    if (res.code === 200) {
        recommendList.value = res.data
    }
}

</script>

<template>
    <view class="p-3">
        <view class="bg-white dark:bg-dark rounded mb-4" v-for="item in recommendList" :key="item.scenicId">
            <view class="flex justify-between items-center p-2">
                <view class="text-lg font-bold">{{ item.scenicName }}</view>
                <view class="text-sm">更多<wd-icon name="a-chevron-rightdouble" size="22px"></wd-icon></view>
            </view>
            <wd-divider custom-class="!my-0 !p-0"></wd-divider>
            <view class="p-2">
                <view class="flex gap-3 mb-2" v-for="items in item.ticketList" :key="items.id">
                    <view class="">
                        <wd-img :src="items.ticketCover" width="80" height="80" radius="5" mode="aspectFill"></wd-img>
                    </view>
                    <view class="flex-1 flex flex-col justify-between">
                        <view>{{ items.ticketName }}</view>
                        <view v-if="items.ticketTags&&items.ticketTags.length">
                            <wd-tag v-for="tag in items.ticketTags" :key="tag" type="primary" plain custom-class="mr-1">{{ tag }}</wd-tag>
                        </view>
                        <view class="flex justify-between items-center">
                            <view>
                                <text class="text-sm text-danger mr-3">￥{{ items.sellingPrice }}</text>
                                <text class="text-xs text-gray-500 line-through">￥{{ items.marketPrice }}</text>
                            </view>
                            <wd-button type="primary" size="small">购买</wd-button>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<style lang="scss" scoped></style>
