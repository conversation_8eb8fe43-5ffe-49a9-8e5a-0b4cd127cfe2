<route lang="jsonc" type="page">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "领券中心"
    }
}</route>

<script lang="ts" setup>
import { useTenantStore } from '@/store';
import { getCouponList, getCouponDetail, receiveCoupon } from '@/api/coupon';
import { onPageScroll, onReachBottom } from '@dcloudio/uni-app';
import UseZPaging from "z-paging/components/z-paging/js/hooks/useZPaging"
import { useEnumStore, useTokenStore } from '@/store';
import { useToast } from 'wot-design-uni';
import showMore from '@/components/showMore/index.vue';
const toast = useToast();

const tenantStore = useTenantStore();
const enumStore = useEnumStore();

const scrollRef = ref(null);
UseZPaging(scrollRef)
const listData = ref<any>([]);

const getList = async (pageNo: number, pageSize: number = 10) => {
    try {
        const res: any = await getCouponList({
            page: pageNo,
            pageSize: pageSize,
            tenantId: tenantStore.tenantInfo.tenantId
        })
        if (res.code === 200) {
            scrollRef.value?.complete(res.data.list)
        } else {
            scrollRef.value?.complete(false)
            uni.showToast({
                title: '获取数据失败',
                icon: 'none'
            })
        }
    } catch (error) {
        scrollRef.value?.complete(false)
        uni.showToast({
            title: '网络请求异常',
            icon: 'none'
        })
    }
}

const turnValidType = (val: any) => {
    if (val.validType == 1) {
        return '永久有效'
    } else {
        return val.validBeginDate + ' 至 ' + val.validEndDate
    }
}
const filterContent = (arr: any, key: string) => {
    console.log(arr, key)
    return arr.map((item: any) => item[key]).join('，')
}

// 去领取
const showInfo = ref(false)
const couponInfo = ref<any>({})
const handleShow = async (val: any) => {
    if (!val.isReceive) {
        toast.show(val.receiveMsg)
        return
    }
    const res = await getCouponDetail({
        id: val.id,
        tenantId: tenantStore.tenantInfo.tenantId
    })
    if (res.code === 200) {
        couponInfo.value = res.data
        showInfo.value = true
    }
}
const handleReceive = async () => {
    const res = await receiveCoupon(couponInfo.value.id)
    if (res.code === 200) {
        uni.showToast({
            title: '领取成功',
            icon: 'success'
        })
        showInfo.value = false
        scrollRef.value?.reload()
    }
}

const value = ref('')
const handleChange = (val: any) => {
    console.log(val, '123456')
    value.value = val
}
</script>

<template>
    <z-paging ref="scrollRef" v-model="listData" @query="getList" :safe-area-inset-bottom="true"
        :use-safe-area-placeholder="true" :show-loading-more-no-more-view="false">
        <view class="p-3">
            <view class="bg-white dark:bg-dark py-2 px-3 pt-6 rounded mb-4 relative" v-for="item in listData" :key="item.id">
                <view class="flex items-center justify-between pb-2">
                    <view>
                        <view class="text-md font-bold">{{ item.couponName }}</view>
                        <view class="text-red-500 text-xs my-2">{{ turnValidType(item) }}</view>
                        <view class="text-gray-500 text-xs">{{ item.ticketScopeType == 1 ? '全部可用' :
                            '部分可用' }}
                            <!-- <wd-tooltip placement="top" :content="filterContent(item.ticketScopeList)"
                                :show-close="true" v-if="item.ticketScopeType == 2">
                                <wd-icon name="info-circle" size="25px"></wd-icon>
                            </wd-tooltip> -->
                        </view>
                    </view>
                    <view class="w-[100px] text-center text-danger">
                        <view class="text-xl font-bold mb-1 leading-none">
                            <text v-if="item.couponType == 1" class="text-xs">￥</text>{{
                                item.couponValue
                            }}<text v-if="item.couponType != 1" class="text-xs">折</text>
                        </view>
                        <view class="text-xs">{{ item.thresholdType == 2 ? '满' + item.thresholdPrice +
                            '可用' :
                            '无门槛'
                        }}</view>
                    </view>
                </view>
                <wd-divider custom-class="!my-0 !p-0" color="rgba(0,0,0,0.1)" dashed></wd-divider>
                <view class="flex justify-between items-center pt-2">
                    <view class="text-xs text-trueGray-500">剩余可领取：{{ item.dayLimit }}</view>
                    <wd-button type="primary" size="small" @click="handleShow(item)">去领取</wd-button>
                </view>
                <view class="text-white absolute top-0 right-0 text-xs px-1.5 py-1 rounded-tr rounded-bl"
                    :class="item.couponType == 1 ? 'bg-danger' : 'bg-warning'">{{ item.couponType
                        == 1 ? '满减券' : '折扣券' }}</view>
            </view>
        </view>
    </z-paging>

    <!-- 详情弹窗 -->
    <wd-popup v-model="showInfo" position="bottom" :safe-area-inset-bottom="true" closable custom-class="rounded-t-xl"
        custom-style="height: auto;" @close="showInfo = false">
        <view class="text-center leading-[2.8] font-bold">{{ couponInfo.couponName }}</view>
        <view class="p-3" v-if="showInfo">
            <view class="flex items-end justify-center mb-2">
                <wd-tag :type="couponInfo.couponType
                    == 1 ? 'danger' : 'warning'">{{ couponInfo.couponType
                        == 1 ? '满减券' : '折扣券' }}</wd-tag>
                <view class="ml-1 text-2xl font-bold leading-none text-danger">
                    <text v-if="couponInfo.couponType == 1" class="text-xs">￥</text>{{ couponInfo.couponValue }}<text
                        v-if="couponInfo.couponType != 1" class="text-xs">折</text>
                </view>
            </view>
            <view class="text-sm">
                <text class="text-gray-500">有效期：</text>{{ turnValidType(couponInfo) }}
            </view>
            <view class="text-sm">
                <text class="text-gray-500">可领取时间：</text>{{ couponInfo.getTimeType == 1 ? '永久有效' :
                    couponInfo.getTimeBeginDate + ' 至 ' +
                    couponInfo.getTimeEndDate }}
            </view>
            <view class="text-sm">
                <text class="text-gray-500">使用门槛：</text>{{ couponInfo.thresholdType == 2 ? '满' +
                    couponInfo.thresholdPrice +
                    '可用' :
                    '无门槛'
                }}
            </view>
            <view class="text-sm">
                <text class="text-gray-500">适用景区：</text>{{ couponInfo.scenicScopeType == 1 ? '全部可用' :
                    filterContent(couponInfo.scenicScopeList, 'scenicName') }}
            </view>
            <view class="text-sm">
                <text class="text-gray-500">适用门票：</text>{{ couponInfo.ticketScopeType == 1 ? '全部可用' :
                    filterContent(couponInfo.ticketScopeList, 'ticketName') }}
            </view>
            <view class="text-sm">
                <text class="text-gray-500">备注：</text>{{ couponInfo.remark }}
            </view>
            <view class="text-sm" v-if="couponInfo.couponDesc">
                <text class="text-gray-500">使用说明：</text>
                <!-- <wd-collapse viewmore custom-class="!p-0" :line-num="2" v-model="value" @change="handleChange">
                    <rich-text :nodes="couponInfo.couponDesc"></rich-text>
                </wd-collapse> -->
                <showMore :lineHeight="30">
                    <rich-text :nodes="couponInfo.couponDesc"></rich-text>
                </showMore>
            </view>
        </view>
        <view class="p-3 bg-white dark:bg-[#1b1b1b] pb-safe">
            <wd-button type="primary" block :disabled="!couponInfo.isReceive" @click="handleReceive">立即领取</wd-button>
        </view>
    </wd-popup>
</template>

<style lang="scss" scoped></style>
