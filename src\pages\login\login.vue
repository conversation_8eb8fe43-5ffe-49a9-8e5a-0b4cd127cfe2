<route lang="jsonc" type="page">{
  "layout": "default",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "登录"
  }
}</route>

<script lang="ts" setup>
import { useTokenStore } from '@/store/token'
import { useUserStore } from '@/store/user'
import { tabbarList } from '@/tabbar/config'
import { ensureDecodeURIComponent } from '@/utils'
import { getTenantConfig, getTenantConfigByAppId } from '@/api/config'
import { useSafeAreaInsets } from '@/hooks/useSafeAreaInsets'
// 获取屏幕边界到安全区域距离
const insets = useSafeAreaInsets()

const redirectUrl = ref('')
onLoad((options) => {
  console.log('login options: ', options)
  if (options.redirect) {
    redirectUrl.value = ensureDecodeURIComponent(options.redirect)
  }
  else {
    redirectUrl.value = tabbarList[0].pagePath
  }
  getTenantConfigData()
  console.log('redirectUrl.value: ', redirectUrl.value)
})

const tokenStore = useTokenStore()
// 微信小程序下登录
async function handleLogin() {
  // #ifdef MP-WEIXIN

  // 微信登录
  const res = await tokenStore.wxLogin()
  console.log('res: ', res)
  if (res.code === 200) {
    // 关闭当前登录页，返回上一页
    uni.navigateBack()
  }
  // #endif
  // #ifndef MP-WEIXIN
  // uni.navigateTo({ url: LOGIN_PAGE })
  // #endif
}

// 暂不登录
function handleNoLogin() {
  uni.navigateBack()
}

const configData = ref<any>({})
// 获取租户配置
const getTenantConfigData = async () => {
  const res: any = await getTenantConfig({ tenantCode: '000' })
  configData.value = res.data?.tenantInfo || {}
}
</script>

<template>
  <view class="h-vh flex flex-col justify-center items-center">
    <view class="">
      <wd-img :src="configData.tenantLogo" width="180" height="180" custom-class="mx-auto block" />
    </view>
    <view class="mt-5 mb-8">
      <view class="text-2xl font-bold">{{ configData.tenantName }}</view>
    </view>
    <view class="w-[60%]">
      <wd-button type="primary" @click="handleLogin" block custom-class="mb-5">
        一键登录
      </wd-button>
      <wd-button type="primary" plain @click="handleNoLogin" block>
        暂不登录
      </wd-button>
    </view>
  </view>
</template>
