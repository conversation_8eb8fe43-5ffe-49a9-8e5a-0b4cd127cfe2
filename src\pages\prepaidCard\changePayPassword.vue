<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "修改支付密码"
    }
}</route>

<script lang="ts" setup>
import { changePrepaidCardPayPassword } from '@/api/prepaidCard'

const formRef = ref(null)
const formData = ref({
    payPassword: '',
    newPayPassword: '',
    confirmPayPassword: ''
})
const formRules = {
    payPassword: [{
        required: true,
        pattern: /\d{6}/,
        message: '请输入6位旧密码',
    }],
    newPayPassword: [{
        required: true,
        pattern: /\d{6}/,
        message: '请输入6位新密码'
    }],
    confirmPayPassword: [{
        required: true,
        pattern: /\d{6}/,
        message: '请确认6位新密码',
        validator: (value) => {
            if (value !== formData.value.newPayPassword) {
                return Promise.reject('两次输入的密码不一致')
            } else {
                return Promise.resolve()
            }
        }
    }]
}

const handleChangePayPassword = () => {
    formRef.value.validate().then(({ valid, errors }) => {
        console.log(valid, errors)
        if (valid) {
            changePrepaidCardPayPassword(formData.value).then((res) => {
                console.log(res)
                uni.showToast({
                    title: '修改成功',
                    icon: 'success',
                    duration: 2000,
                    success: () => {
                        setTimeout(() => {
                            uni.navigateBack()
                        }, 2000)
                    }
                })
            }).catch((error) => {
                console.log(error)
            })
        }
    }).catch((error) => {
        console.log(error)
    })
}
</script>

<template>
    <view class="p-3">
        <view class="p-2 rounded">
            <wd-form ref="formRef" :model="formData" :rules="formRules">
                <wd-cell-group :border="true">
                    <wd-input label="旧密码" label-width="150rpx" prop="payPassword" v-model="formData.payPassword"
                        placeholder="请输入6位旧密码" clearable show-password />
                    <wd-input label="新密码" label-width="150rpx" prop="newPayPassword" v-model="formData.newPayPassword"
                        placeholder="请输入6位新密码" clearable show-password />
                    <wd-input label="确认密码" label-width="150rpx" prop="confirmPayPassword"
                        v-model="formData.confirmPayPassword" placeholder="请确认6位新密码" clearable show-password />
                </wd-cell-group>
            </wd-form>
        </view>
        <view class="text-center mb-5">
            <wd-text text="初始密码为：123456" custom-class="text-xs"></wd-text>
        </view>
        <view class="text-center px-2">
            <wd-button type="primary" block @click="handleChangePayPassword">确定修改</wd-button>
        </view>
    </view>
</template>

<style lang="scss" scoped></style>
