<route lang="jsonc">{
    "layout": "custom",
    "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "景点详情"
    }
}</route>

<script lang="ts" setup>
import { getScenicSpotInfo } from '@/api/scenic';
import { useSafeAreaInsets } from '@/hooks/useSafeAreaInsets';
import { useTenantStore } from '@/store';
import AudioPlayer from './components/audioPlayer.vue';

const insets = useSafeAreaInsets();
const tenantStore = useTenantStore();
onLoad((options) => {
    getInfo(options.id, options.scenicId)
})
const infoData = ref<any>({})
const getInfo = async (id: string, scenicId: string) => {
    const res = await getScenicSpotInfo({ id: id, tenantId: tenantStore.tenantInfo.tenantId, scenicId: scenicId })
    if (res.code === 200) {
        infoData.value = res.data
        infoData.value.spotImage = infoData.value.spotImage.split(',')
    }
}
onPageScroll((e: any) => { })
const openMap = () => {
    uni.openLocation({
        latitude: Number(infoData.value.latitude),  // 纬度，范围为-90~90，负数表示南纬。
        longitude: Number(infoData.value.longitude), // 经度，范围为-180~180，负数表示西经。
        name: infoData.value.scenicName,
        address: infoData.value.address,
        success: (res) => {
            console.log(res);
        },
        fail: (fail) => {
            console.log('fail ', fail)
        },
    });
}

defineExpose({
    title: '景点详情',
})
</script>

<template>
    <view>
        <view class="flex">
            <wd-swiper :list="infoData.spotImage" height="210px" custom-class="w-full rounded-0"
                :indicator="{ type: 'dots-bar' }" stopAutoplayWhenVideoPlay :autoplayVideo="false" :muted="false">
            </wd-swiper>
        </view>
        <view class="bg-[#f6f6f6] dark:bg-black">
            <view class="p-3 bg-white dark:bg-dark mb-3">
                <view class="flex items-center justify-between mb-2">
                    <view class="text-lg font-bold">{{ infoData.spotName }}</view>
                    <wd-button type="icon" icon="share" size="small">分享</wd-button>
                </view>
                <view class="text-sm text-gray-500 mb-2">
                    <view>{{ infoData.businessHours }}</view>
                </view>
                <view>
                    <AudioPlayer v-if="infoData.spotAudio" :src="infoData.spotAudio"
                        :title="infoData.spotName + '解说'" />
                </view>
                <view
                    class="bg-[url(https://ticketoss.yunvip123.com/ticket/mapbg.png)] bg-no-repeat bg-center bg-cover h-[48px] flex items-center justify-between px-2">
                    <view class="text-xs">
                        {{ infoData.spotAddress }}
                    </view>
                    <view class="text-xs text-center flex-shrink-0" @click="openMap">
                        <view class="bg-white dark:bg-dark rounded-full p-1 flex items-center justify-center mb-0.5">
                            <text class="i-carbon-map" />
                        </view>
                        <text class="text-2xs">地图</text>
                    </view>
                </view>
            </view>
            <view class="p-3 bg-white dark:bg-dark">
                <view class="text-lg font-bold mb-2">景点介绍</view>
                <view class="">
                    <rich-text :nodes="infoData.introduce"></rich-text>
                </view>
            </view>
        </view>
    </view>
</template>

<style lang="scss">
swiper {
    border-radius: 0 !important;
}
</style>