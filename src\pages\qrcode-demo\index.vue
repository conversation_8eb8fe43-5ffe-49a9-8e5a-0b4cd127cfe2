<route lang="jsonc">{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "二维码组件演示"
  }
}</route>

<script lang="ts" setup>
import { ref } from 'vue'
import CusQrCode from '@/components/cusQrCode/index.vue'

// 演示数据
const qrText = ref('https://unibest.tech')
const qrSize = ref(200)
const errorLevel = ref<'L' | 'M' | 'Q' | 'H'>('M')
const foregroundColor = ref('#000000')
const backgroundColor = ref('#ffffff')
const margin = ref(4)
const logoUrl = ref('')
const logoSize = ref(0.2)
const format = ref<'canvas' | 'svg' | 'image'>('canvas')
const showDownload = ref(true)
const showCopy = ref(true)
const responsive = ref(false)

// 预设示例
const examples = [
  {
    name: '网站链接',
    value: 'https://unibest.tech',
    description: '官网地址'
  },
  {
    name: '微信号',
    value: 'wechat:feige996',
    description: '微信联系方式'
  },
  {
    name: 'WiFi信息',
    value: 'WIFI:T:WPA;S:MyNetwork;P:password123;;',
    description: 'WiFi连接信息'
  },
  {
    name: '联系人信息',
    value: 'BEGIN:VCARD\nVERSION:3.0\nFN:张三\nTEL:13800138000\nEMAIL:<EMAIL>\nEND:VCARD',
    description: '电子名片'
  }
]

// 颜色预设
const colorPresets = [
  { name: '经典黑白', fg: '#000000', bg: '#ffffff' },
  { name: '蓝色主题', fg: '#1890ff', bg: '#f0f8ff' },
  { name: '绿色主题', fg: '#52c41a', bg: '#f6ffed' },
  { name: '红色主题', fg: '#ff4d4f', bg: '#fff1f0' },
  { name: '紫色主题', fg: '#722ed1', bg: '#f9f0ff' }
]

// 事件处理
const onQrGenerated = (dataUrl: string) => {
  console.log('二维码生成成功:', dataUrl)
  uni.showToast({
    title: '生成成功',
    icon: 'success'
  })
}

const onQrError = (error: Error) => {
  console.error('二维码生成失败:', error)
  uni.showToast({
    title: '生成失败',
    icon: 'none'
  })
}

const onDownloaded = (filename: string) => {
  console.log('下载完成:', filename)
}

const onCopied = () => {
  console.log('内容已复制')
}

// 应用示例
const applyExample = (example: typeof examples[0]) => {
  qrText.value = example.value
}

// 应用颜色预设
const applyColorPreset = (preset: typeof colorPresets[0]) => {
  foregroundColor.value = preset.fg
  backgroundColor.value = preset.bg
}

// 重置为默认值
const resetToDefault = () => {
  qrText.value = 'https://unibest.tech'
  qrSize.value = 200
  errorLevel.value = 'M'
  foregroundColor.value = '#000000'
  backgroundColor.value = '#ffffff'
  margin.value = 4
  logoUrl.value = ''
  logoSize.value = 0.2
  format.value = 'canvas'
  showDownload.value = true
  showCopy.value = true
  responsive.value = false
}

// 事件处理函数
const onSizeChange = (e: any) => {
  qrSize.value = e.detail.value
}

const onErrorLevelChange = (e: any) => {
  const levels = ['L', 'M', 'Q', 'H']
  errorLevel.value = levels[e.detail.value] as 'L' | 'M' | 'Q' | 'H'
}

const onFormatChange = (e: any) => {
  const formats = ['canvas', 'svg', 'image']
  format.value = formats[e.detail.value] as 'canvas' | 'svg' | 'image'
}

const onMarginChange = (e: any) => {
  margin.value = e.detail.value
}

const onLogoSizeChange = (e: any) => {
  logoSize.value = e.detail.value
}

const onShowDownloadChange = (e: any) => {
  showDownload.value = e.detail.value
}

const onShowCopyChange = (e: any) => {
  showCopy.value = e.detail.value
}

const onResponsiveChange = (e: any) => {
  responsive.value = e.detail.value
}
</script>

<template>
  <view class="demo-container">
    <!-- 二维码展示区域 -->
    <view class="qr-display-section">
      <view class="section-title">二维码预览</view>
      <view class="qr-wrapper">
        <CusQrCode
          :value="qrText"
          :size="qrSize"
          :error-correction-level="errorLevel"
          :color="foregroundColor"
          :background-color="backgroundColor"
          :margin="margin"
          :logo="logoUrl"
          :logo-size="logoSize"
          :format="format"
          :show-download="showDownload"
          :show-copy="showCopy"
          :responsive="responsive"
          download-name="my-qrcode"
          @generated="onQrGenerated"
          @error="onQrError"
          @downloaded="onDownloaded"
          @copied="onCopied"
        />
      </view>
    </view>

    <!-- 配置区域 -->
    <view class="config-section">
      <view class="section-title">配置选项</view>
      
      <!-- 基础配置 -->
      <view class="config-group">
        <view class="group-title">基础设置</view>
        
        <view class="config-item">
          <text class="label">二维码内容:</text>
          <textarea 
            v-model="qrText" 
            class="textarea-input"
            placeholder="请输入要生成二维码的内容"
            :maxlength="1000"
          />
        </view>
        
        <view class="config-item">
          <text class="label">尺寸: {{ qrSize }}px</text>
          <slider
            :value="qrSize"
            @change="onSizeChange"
            :min="100"
            :max="400"
            :step="10"
            class="slider"
          />
        </view>

        <view class="config-item">
          <text class="label">错误纠正级别:</text>
          <picker
            :value="['L', 'M', 'Q', 'H'].indexOf(errorLevel)"
            @change="onErrorLevelChange"
            :range="['L', 'M', 'Q', 'H']"
            class="picker"
          >
            <view class="picker-text">{{ errorLevel }}</view>
          </picker>
        </view>

        <view class="config-item">
          <text class="label">输出格式:</text>
          <picker
            :value="['canvas', 'svg', 'image'].indexOf(format)"
            @change="onFormatChange"
            :range="['canvas', 'svg', 'image']"
            class="picker"
          >
            <view class="picker-text">{{ format }}</view>
          </picker>
        </view>
      </view>

      <!-- 颜色配置 -->
      <view class="config-group">
        <view class="group-title">颜色设置</view>
        
        <view class="config-item">
          <text class="label">前景色:</text>
          <input 
            v-model="foregroundColor" 
            type="text" 
            class="color-input"
            placeholder="#000000"
          />
        </view>
        
        <view class="config-item">
          <text class="label">背景色:</text>
          <input 
            v-model="backgroundColor" 
            type="text" 
            class="color-input"
            placeholder="#ffffff"
          />
        </view>
        
        <view class="color-presets">
          <text class="label">颜色预设:</text>
          <view class="preset-buttons">
            <button 
              v-for="preset in colorPresets" 
              :key="preset.name"
              @click="applyColorPreset(preset)"
              class="preset-btn"
              size="mini"
            >
              {{ preset.name }}
            </button>
          </view>
        </view>
      </view>

      <!-- 高级配置 -->
      <view class="config-group">
        <view class="group-title">高级设置</view>
        
        <view class="config-item">
          <text class="label">边距: {{ margin }}</text>
          <slider
            :value="margin"
            @change="onMarginChange"
            :min="0"
            :max="10"
            :step="1"
            class="slider"
          />
        </view>

        <view class="config-item">
          <text class="label">Logo URL:</text>
          <input
            v-model="logoUrl"
            type="text"
            class="text-input"
            placeholder="输入Logo图片地址"
          />
        </view>

        <view class="config-item" v-if="logoUrl">
          <text class="label">Logo大小: {{ (logoSize * 100).toFixed(0) }}%</text>
          <slider
            :value="logoSize"
            @change="onLogoSizeChange"
            :min="0.1"
            :max="0.5"
            :step="0.05"
            class="slider"
          />
        </view>

        <view class="config-item">
          <label class="checkbox-label">
            <checkbox :checked="showDownload" @change="onShowDownloadChange" />
            <text>显示下载按钮</text>
          </label>
        </view>

        <view class="config-item">
          <label class="checkbox-label">
            <checkbox :checked="showCopy" @change="onShowCopyChange" />
            <text>显示复制按钮</text>
          </label>
        </view>

        <view class="config-item">
          <label class="checkbox-label">
            <checkbox :checked="responsive" @change="onResponsiveChange" />
            <text>响应式布局</text>
          </label>
        </view>
      </view>

      <!-- 示例模板 -->
      <view class="config-group">
        <view class="group-title">示例模板</view>
        <view class="example-buttons">
          <button 
            v-for="example in examples" 
            :key="example.name"
            @click="applyExample(example)"
            class="example-btn"
            size="mini"
          >
            <view class="example-name">{{ example.name }}</view>
            <view class="example-desc">{{ example.description }}</view>
          </button>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <button @click="resetToDefault" class="reset-btn">
          重置为默认
        </button>
      </view>
    </view>
  </view>
</template>

<style scoped>
.demo-container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.qr-display-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333333;
}

.qr-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 220px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

.config-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-group {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.config-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #666666;
}

.config-item {
  margin-bottom: 12px;
}

.label {
  display: block;
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
}

.textarea-input {
  width: 100%;
  min-height: 60px;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
}

.text-input,
.color-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.slider {
  width: 100%;
  margin-top: 8px;
}

.picker {
  width: 100%;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #ffffff;
}

.picker-text {
  font-size: 14px;
  color: #333333;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333333;
}

.color-presets {
  margin-top: 8px;
}

.preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.preset-btn {
  background-color: #f0f0f0;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #333333;
}

.preset-btn:active {
  background-color: #1890ff;
  color: #ffffff;
}

.example-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-btn {
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
  text-align: left;
}

.example-btn:active {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.example-name {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4px;
}

.example-desc {
  font-size: 12px;
  color: #666666;
}

.action-section {
  margin-top: 24px;
  text-align: center;
}

.reset-btn {
  background-color: #ff4d4f;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 14px;
}

.reset-btn:active {
  background-color: #d9363e;
}
</style>
