<route lang="jsonc" type="page">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "公告详情"
    }
}</route>

<script lang="ts" setup>
import { getNoticeInfo } from '@/api/notice';
import { useTenantStore } from '@/store';
const tenantStore = useTenantStore()

onLoad((options) => {
    getInfo(options.id)
})
const InfoData = ref<any>({})
const getInfo = async (id: any) => {
    const res = await getNoticeInfo({
        id: id,
        tenantId: tenantStore.tenantInfo.tenantId
    })
    if (res.code === 200) {
        InfoData.value = res.data
    }
}
</script>

<template>
    <view class="p-3">
        <view class="bg-white dark:bg-dark rounded p-2">
            <view class="text-xl font-bold mb-2">{{ InfoData.title }}</view>
            <view class="text-sm text-gray-500 mb-2">
                <wd-icon name="calendar" size="12" class="mr-1"></wd-icon>
                {{ InfoData.createdAt }}
            </view>
            <view>
                <rich-text :nodes="InfoData.content"></rich-text>
            </view>
        </view>
    </view>
</template>

<style lang="scss" scoped></style>
