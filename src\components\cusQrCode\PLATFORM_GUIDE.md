# CusQrCode 平台兼容性指南

## 🚨 Canvas-ID 问题解决方案

### 问题描述
在 UniApp 中使用 `<canvas>` 元素时，需要提供 `canvas-id` 属性，特别是在小程序平台上。

### 解决方案

#### 1. 添加 canvas-id 属性
```vue
<canvas 
  :canvas-id="canvasId"
  :width="size"
  :height="size"
  ref="canvasRef"
/>
```

#### 2. 生成唯一 ID
```typescript
const canvasId = `qr-canvas-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
```

## 🌐 平台差异处理

### H5 平台
- ✅ 支持完整的 Canvas API
- ✅ 支持 SVG 格式
- ✅ 支持浏览器下载
- ✅ 支持 Clipboard API

```typescript
// #ifdef H5
await QRCode.toCanvas(canvasRef.value, props.value, qrOptions.value)
// #endif
```

### 小程序平台
- ⚠️ Canvas API 有限制
- ❌ 不支持 SVG
- ⚠️ 使用 uni.createCanvasContext()
- ✅ 保存到相册

```typescript
// #ifndef H5
const dataUrl = await QRCode.toDataURL(props.value, qrOptions.value)
// #endif
```

### App 平台
- ✅ 支持大部分 Canvas API
- ❌ 不支持 SVG
- ✅ 保存到相册
- ✅ 系统剪贴板

## 🔧 推荐配置

### 通用配置
```vue
<CusQrCode
  value="https://example.com"
  :size="200"
  format="canvas"  <!-- 推荐使用 canvas -->
  error-correction-level="M"
/>
```

### H5 专用功能
```vue
<CusQrCode
  value="https://example.com"
  format="svg"  <!-- H5 可以使用 SVG -->
  :show-download="true"
  :show-copy="true"
/>
```

### 小程序优化
```vue
<CusQrCode
  value="https://example.com"
  format="image"  <!-- 小程序推荐使用 image -->
  :show-download="false"  <!-- 小程序不支持直接下载 -->
  :show-copy="true"
/>
```

## 🐛 常见问题

### 1. canvas-id attribute is undefined
**原因**: 缺少 canvas-id 属性
**解决**: 确保 canvas 元素有 canvas-id 属性

### 2. Canvas 不显示
**原因**: 小程序平台 Canvas API 限制
**解决**: 使用 format="image" 或 format="canvas" 配合条件编译

### 3. 下载功能不工作
**原因**: 平台不支持浏览器下载
**解决**: 使用平台特定的保存方法

### 4. SVG 格式不显示
**原因**: 小程序不支持 SVG
**解决**: 在小程序中使用 canvas 或 image 格式

## 📱 平台特定代码示例

### 条件编译示例
```vue
<template>
  <!-- H5 平台 -->
  <!-- #ifdef H5 -->
  <canvas ref="canvasRef" :canvas-id="canvasId" />
  <!-- #endif -->
  
  <!-- 小程序平台 -->
  <!-- #ifdef MP -->
  <image :src="qrDataUrl" mode="aspectFit" />
  <!-- #endif -->
  
  <!-- App 平台 -->
  <!-- #ifdef APP-PLUS -->
  <canvas ref="canvasRef" :canvas-id="canvasId" />
  <!-- #endif -->
</template>
```

### 平台检测
```typescript
// 检测当前平台
const isH5 = process.env.UNI_PLATFORM === 'h5'
const isMP = process.env.UNI_PLATFORM.startsWith('mp-')
const isApp = process.env.UNI_PLATFORM === 'app-plus'

// 根据平台选择格式
const recommendedFormat = computed(() => {
  if (isH5) return 'canvas'  // H5 支持所有格式
  if (isMP) return 'image'   // 小程序推荐 image
  return 'canvas'            // App 推荐 canvas
})
```

## 🎯 最佳实践

1. **优先使用 Canvas 格式**: 兼容性最好
2. **为小程序提供降级方案**: 使用 image 格式
3. **合理使用条件编译**: 针对不同平台优化
4. **测试所有目标平台**: 确保功能正常
5. **提供用户友好的错误提示**: 当功能不可用时

## 🔄 迁移指南

### 从旧版本升级
1. 添加 canvas-id 属性
2. 更新平台特定代码
3. 测试所有平台功能
4. 更新文档和示例

### 新项目集成
1. 选择合适的输出格式
2. 配置平台特定功能
3. 添加错误处理
4. 测试用户体验
