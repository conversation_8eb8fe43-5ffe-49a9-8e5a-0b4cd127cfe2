<route lang="jsonc" type="page">{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "二维码测试"
  }
}</route>

<script lang="ts" setup>
import { ref } from 'vue'
import CusQrCode from '@/components/cusQrCode/index.vue'

const testValue = ref('https://unibest.tech')
const qrRef = ref()

const onGenerated = (dataUrl: string) => {
  console.log('QR Code generated:', dataUrl.substring(0, 50) + '...')
  uni.showToast({
    title: '生成成功',
    icon: 'success'
  })
}

const onError = (error: Error) => {
  console.error('QR Code error:', error)
  uni.showToast({
    title: '生成失败',
    icon: 'none'
  })
}

const testGenerate = () => {
  if (qrRef.value) {
    qrRef.value.generateQRCode()
  }
}

const testDownload = () => {
  if (qrRef.value) {
    qrRef.value.downloadQRCode()
  }
}

const testCopy = () => {
  if (qrRef.value) {
    qrRef.value.copyContent()
  }
}
</script>

<template>
  <view class="test-container">
    <view class="test-section">
      <view class="section-title">基础测试</view>
      <view class="qr-container">
        <CusQrCode
          ref="qrRef"
          :value="testValue"
          :size="200"
          :show-download="true"
          :show-copy="true"
          @generated="onGenerated"
          @error="onError"
        />
      </view>
      
      <view class="input-section">
        <input 
          v-model="testValue" 
          placeholder="输入二维码内容"
          class="test-input"
        />
      </view>
      
      <view class="button-section">
        <button @click="testGenerate" class="test-btn">重新生成</button>
        <button @click="testDownload" class="test-btn">下载</button>
        <button @click="testCopy" class="test-btn">复制</button>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">样式测试</view>
      <view class="style-tests">
        <view class="style-item">
          <text class="style-label">蓝色主题</text>
          <CusQrCode
            value="蓝色主题测试"
            :size="120"
            color="#1890ff"
            background-color="#f0f8ff"
          />
        </view>
        
        <view class="style-item">
          <text class="style-label">绿色主题</text>
          <CusQrCode
            value="绿色主题测试"
            :size="120"
            color="#52c41a"
            background-color="#f6ffed"
          />
        </view>
        
        <view class="style-item">
          <text class="style-label">高容错</text>
          <CusQrCode
            value="高容错测试"
            :size="120"
            error-correction-level="H"
          />
        </view>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">格式测试</view>
      <view class="format-tests">
        <view class="format-item">
          <text class="format-label">Canvas</text>
          <CusQrCode
            value="Canvas格式测试"
            :size="100"
            format="canvas"
          />
        </view>
        
        <view class="format-item">
          <text class="format-label">SVG</text>
          <CusQrCode
            value="SVG格式测试"
            :size="100"
            format="svg"
          />
        </view>
        
        <view class="format-item">
          <text class="format-label">Image</text>
          <CusQrCode
            value="Image格式测试"
            :size="100"
            format="image"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.test-container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333333;
  border-left: 4px solid #1890ff;
  padding-left: 8px;
}

.qr-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.input-section {
  margin-bottom: 16px;
}

.test-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  background-color: #ffffff;
}

.button-section {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.test-btn {
  background-color: #1890ff;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
}

.test-btn:active {
  background-color: #096dd9;
}

.style-tests {
  display: flex;
  gap: 16px;
  justify-content: space-around;
  flex-wrap: wrap;
}

.style-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.style-label {
  font-size: 12px;
  color: #666666;
  text-align: center;
}

.format-tests {
  display: flex;
  gap: 16px;
  justify-content: space-around;
  flex-wrap: wrap;
}

.format-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.format-label {
  font-size: 12px;
  color: #666666;
  text-align: center;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-container {
    padding: 8px;
  }
  
  .test-section {
    padding: 12px;
  }
  
  .style-tests,
  .format-tests {
    gap: 12px;
  }
  
  .button-section {
    gap: 8px;
  }
  
  .test-btn {
    padding: 8px 16px;
    font-size: 12px;
  }
}
</style>
