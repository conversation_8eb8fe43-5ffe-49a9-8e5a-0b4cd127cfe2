<!-- 通过qrcode封装生成二维码 -->
<script lang="ts" setup>
import QRCode from 'qrcode'
import { ref, watch, nextTick, computed, onMounted } from 'vue'

// Props 定义
interface Props {
  // 二维码内容
  value: string
  // 二维码大小
  size?: number
  // 错误纠正级别 L M Q H
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H'
  // 前景色
  color?: string
  // 背景色
  backgroundColor?: string
  // 边距
  margin?: number
  // 是否显示下载按钮
  showDownload?: boolean
  // 下载文件名
  downloadName?: string
  // 是否显示复制按钮
  showCopy?: boolean
  // logo图片地址
  logo?: string
  // logo大小比例 (0-1)
  logoSize?: number
  // 输出格式
  format?: 'canvas' | 'svg' | 'image'
  // 是否自适应容器
  responsive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  size: 200,
  errorCorrectionLevel: 'M',
  color: '#000000',
  backgroundColor: '#ffffff',
  margin: 4,
  showDownload: false,
  downloadName: 'qrcode',
  showCopy: false,
  logo: '',
  logoSize: 0.2,
  format: 'canvas',
  responsive: false
})

// Emits 定义
const emit = defineEmits<{
  generated: [dataUrl: string]
  error: [error: Error]
  downloaded: [filename: string]
  copied: []
}>()

// 响应式数据
const canvasRef = ref<HTMLCanvasElement>()
const svgRef = ref<HTMLDivElement>()
const imageRef = ref<HTMLImageElement>()
const qrDataUrl = ref('')
const isGenerating = ref(false)
const errorMessage = ref('')

// 生成唯一的canvas-id
const canvasId = `qr-canvas-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`

// 计算属性
const containerStyle = computed(() => {
  if (props.responsive) {
    return {
      width: '100%',
      maxWidth: `${props.size}px`,
      aspectRatio: '1'
    }
  }
  return {
    width: `${props.size}px`,
    height: `${props.size}px`
  }
})

// QR码生成选项
const qrOptions = computed(() => ({
  errorCorrectionLevel: props.errorCorrectionLevel,
  color: {
    dark: props.color,
    light: props.backgroundColor
  },
  margin: props.margin,
  width: props.size
}))

// 生成二维码
const generateQRCode = async () => {
  if (!props.value.trim()) {
    errorMessage.value = '请输入要生成二维码的内容'
    return
  }

  isGenerating.value = true
  errorMessage.value = ''

  try {
    if (props.format === 'canvas') {
      await generateCanvas()
    } else if (props.format === 'svg') {
      await generateSVG()
    } else if (props.format === 'image') {
      await generateImage()
    }
  } catch (error) {
    const err = error as Error
    errorMessage.value = err.message
    emit('error', err)
  } finally {
    isGenerating.value = false
  }
}

// 生成Canvas格式
const generateCanvas = async () => {
  // #ifdef H5
  if (!canvasRef.value) return

  await QRCode.toCanvas(canvasRef.value, props.value, qrOptions.value)

  // 如果有logo，添加logo
  if (props.logo) {
    await addLogoToCanvas()
  }

  qrDataUrl.value = canvasRef.value.toDataURL('image/png')
  emit('generated', qrDataUrl.value)
  // #endif

  // #ifndef H5
  // 在小程序和App中，直接生成DataURL
  try {
    const dataUrl = await QRCode.toDataURL(props.value, qrOptions.value)
    qrDataUrl.value = dataUrl
    emit('generated', dataUrl)
  } catch (error) {
    throw error
  }
  // #endif
}

// 生成SVG格式
const generateSVG = async () => {
  if (!svgRef.value) return

  const svgString = await QRCode.toString(props.value, {
    ...qrOptions.value,
    type: 'svg'
  })

  svgRef.value.innerHTML = svgString
  emit('generated', svgString)
}

// 生成Image格式
const generateImage = async () => {
  if (!imageRef.value) return

  const dataUrl = await QRCode.toDataURL(props.value, qrOptions.value)
  imageRef.value.src = dataUrl
  qrDataUrl.value = dataUrl
  emit('generated', dataUrl)
}

// 添加Logo到Canvas
const addLogoToCanvas = async () => {
  if (!canvasRef.value || !props.logo) return

  const canvas = canvasRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  return new Promise<void>((resolve, reject) => {
    const logoImg = new Image()
    logoImg.crossOrigin = 'anonymous'

    logoImg.onload = () => {
      try {
        const logoSizePixels = canvas.width * props.logoSize
        const x = (canvas.width - logoSizePixels) / 2
        const y = (canvas.height - logoSizePixels) / 2

        // 绘制白色背景圆形
        ctx.fillStyle = props.backgroundColor
        ctx.beginPath()
        ctx.arc(canvas.width / 2, canvas.height / 2, logoSizePixels / 2 + 5, 0, 2 * Math.PI)
        ctx.fill()

        // 绘制logo
        ctx.drawImage(logoImg, x, y, logoSizePixels, logoSizePixels)
        resolve()
      } catch (error) {
        reject(error)
      }
    }

    logoImg.onerror = () => {
      reject(new Error('Logo图片加载失败'))
    }

    logoImg.src = props.logo
  })
}

// 下载二维码
const downloadQRCode = () => {
  if (!qrDataUrl.value) {
    uni.showToast({
      title: '请先生成二维码',
      icon: 'none'
    })
    return
  }

  // 在H5环境下使用浏览器下载
  // #ifdef H5
  const link = document.createElement('a')
  link.download = `${props.downloadName}.png`
  link.href = qrDataUrl.value
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  // #endif

  // 在小程序和App环境下保存到相册
  // #ifndef H5
  uni.saveImageToPhotosAlbum({
    filePath: qrDataUrl.value,
    success: () => {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  })
  // #endif

  emit('downloaded', `${props.downloadName}.png`)
}

// 复制二维码内容
const copyContent = () => {
  // #ifdef H5
  if (navigator.clipboard) {
    navigator.clipboard.writeText(props.value).then(() => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
      emit('copied')
    }).catch(() => {
      uni.showToast({
        title: '复制失败',
        icon: 'none'
      })
    })
  } else {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = props.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    uni.showToast({
      title: '复制成功',
      icon: 'success'
    })
    emit('copied')
  }
  // #endif

  // #ifndef H5
  uni.setClipboardData({
    data: props.value,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
      emit('copied')
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none'
      })
    }
  })
  // #endif
}

// 监听props变化，自动重新生成
watch(() => [props.value, props.size, props.errorCorrectionLevel, props.color, props.backgroundColor, props.margin, props.logo], () => {
  if (props.value) {
    nextTick(() => {
      generateQRCode()
    })
  }
}, { deep: true })

// 组件挂载后生成二维码
onMounted(() => {
  if (props.value) {
    nextTick(() => {
      generateQRCode()
    })
  }
})

// 暴露方法给父组件
defineExpose({
  generateQRCode,
  downloadQRCode,
  copyContent
})
</script>

<template>
  <view class="qr-code-container" :style="containerStyle">
    <!-- 错误提示 -->
    <view v-if="errorMessage" class="error-message">
      <text class="error-text">{{ errorMessage }}</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="isGenerating" class="loading-container">
      <text class="loading-text">生成中...</text>
    </view>

    <!-- Canvas格式 -->
    <canvas
      v-if="format === 'canvas'"
      ref="canvasRef"
      :canvas-id="canvasId"
      class="qr-canvas"
      :style="{ width: '100%', height: '100%' }"
    />

    <!-- SVG格式 -->
    <view
      v-if="format === 'svg'"
      ref="svgRef"
      class="qr-svg"
      :style="{ width: '100%', height: '100%' }"
    />

    <!-- Image格式 -->
    <image
      v-if="format === 'image'"
      ref="imageRef"
      class="qr-image"
      mode="aspectFit"
      :style="{ width: '100%', height: '100%' }"
    />

    <!-- 操作按钮 -->
    <view v-if="(showDownload || showCopy) && qrDataUrl" class="action-buttons">
      <button
        v-if="showDownload"
        @click="downloadQRCode"
        class="action-btn download-btn"
        size="mini"
      >
        下载
      </button>

      <button
        v-if="showCopy"
        @click="copyContent"
        class="action-btn copy-btn"
        size="mini"
      >
        复制内容
      </button>
    </view>
  </view>
</template>

<style scoped>
.qr-code-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 0, 0, 0.1);
  border: 1px solid #ff4444;
  border-radius: 4px;
  padding: 8px 12px;
  z-index: 10;
}

.error-text {
  color: #ff4444;
  font-size: 12px;
}

.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  padding: 8px 12px;
  z-index: 10;
}

.loading-text {
  color: #ffffff;
  font-size: 12px;
}

.qr-canvas,
.qr-svg,
.qr-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.action-buttons {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  z-index: 5;
}

.action-btn {
  background-color: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.action-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

.download-btn:active {
  background-color: #007aff;
}

.copy-btn:active {
  background-color: #34c759;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    bottom: 4px;
    right: 4px;
    gap: 4px;
  }

  .action-btn {
    padding: 2px 6px;
    font-size: 9px;
  }
}
</style>