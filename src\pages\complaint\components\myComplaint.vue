<script lang="ts" setup>
import { getComplaintList } from '@/api/complaint';
import UseZPaging from "z-paging/components/z-paging/js/hooks/useZPaging"
import { useEnumStore } from '@/store';
const enumStore = useEnumStore();
const scrollRef = ref(null);
UseZPaging(scrollRef)
const listData = ref<any>([]);
const getList = async (pageNo: number, pageSize: number = 10) => {
    try {
        const res: any = await getComplaintList({
            page: pageNo,
            pageSize: pageSize
        })
        if (res.code === 200) {
            scrollRef.value?.complete(res.data.list)
        } else {
            scrollRef.value?.complete(false)
            uni.showToast({
                title: '获取数据失败',
                icon: 'none'
            })
        }
    } catch (error) {
        scrollRef.value?.complete(false)
        uni.showToast({
            title: '网络请求异常',
            icon: 'none'
        })
    }
}
const goDetail = (item) => {
    uni.navigateTo({
        url: `/pages/complaint/details?id=${item.id}`
    })
}
const filterTxt = (val: any) => {
    let arr = enumStore.enumData?.complaintType?.list
    return arr.find(item => item.value === val)?.label
}
</script>

<template>
    <view class="h-full w-full">
        <z-paging ref="scrollRef" v-model="listData" @query="getList" :safe-area-inset-bottom="true"
            :use-safe-area-placeholder="true" :use-page-scroll="true" :show-loading-more-no-more-view="false">
            <view class="p-3">
                <view class="bg-white dark:bg-dark p-3 rounded mb-4" v-for="item in listData" :key="item.id" @click="goDetail(item)">
                    <view class="flex justify-between items-center">
                        <text class="mb-2 font-medium">{{ item.scenicInfo.scenicName }}（{{ filterTxt(item.complaintType)
                            }}）</text>
                        <wd-tag :type="item.complaintStatus == 1 ? 'primary' : 'success'" plain>{{ item.complaintStatus
                            == 1 ? '未办结' : '已办结' }}</wd-tag>
                    </view>
                    <wd-text :text="item.complaintContent" :lines="2" custom-class="text-sm mb-2"></wd-text>
                    <view class="text-sm text-gray-500">{{ item.createdAt }}</view>
                </view>
            </view>
        </z-paging>
    </view>
</template>