<script lang="ts" setup>
import { useTheme } from '@/composables/useTheme'
const { isDark } = useTheme()
const goBack = () => {
  // 判断是否是首页
  if (getCurrentPages().length === 1) {
    uni.switchTab({
      url: '/pages/index/index',
    })
    return
  }
  uni.navigateBack()
}
const scrollTop = ref(0)
onPageScroll((e: any) => {
  scrollTop.value = e.scrollTop
})
const page = getCurrentPages().pop()
console.log(page.$vm,'title')
const title = computed(() => {
  return scrollTop.value < 100 ? '' : page.$vm.title
})
</script>

<template>
  <view class="h-full">
    <!-- // 导航栏 -->
    <wd-navbar :fixed="true" :bordered="false" left-arrow :safeAreaInsetTop="true"
      :custom-style="isDark ? `background-color: rgba(0, 0, 0, ${scrollTop / 100}); !important;` : `background-color: rgba(255, 255, 255, ${scrollTop / 100}); !important; `"
      @click-left="goBack">
      <template #title>
        <view class="text-sm leading-[44px] font-500">{{ title }}</view>
      </template>
    </wd-navbar>
    <slot />
  </view>
</template>