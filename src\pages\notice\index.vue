<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "公告"
    }
}</route>

<script lang="ts" setup>
import { getNoticeList } from '@/api/notice';
import { onPageScroll, onReachBottom } from '@dcloudio/uni-app';
import UseZPaging from "z-paging/components/z-paging/js/hooks/useZPaging"
import { useTenantStore } from '@/store';
const tenantStore = useTenantStore()
const scrollRef = ref(null)
UseZPaging(scrollRef)
const listData = ref<any>([])
const getList = async (pageNo: number, pageSize: number = 10) => {
    try {
        const res: any = await getNoticeList({
            page: pageNo,
            pageSize: pageSize,
            tenantId: tenantStore.tenantInfo.tenantId
        })
        if (res.code === 200) {
            scrollRef.value?.complete(res.data.list)
        } else {
            scrollRef.value?.complete(false)
            uni.showToast({
                title: '获取数据失败',
                icon: 'none'
            })
        }
    } catch (error) {
        scrollRef.value?.complete(false)
        uni.showToast({
            title: '网络请求异常',
            icon: 'none'
        })
    }
}

const goDetail = (item: any) => {
    uni.navigateTo({
        url: `/pages/notice/detail?id=${item.id}`
    })
}

</script>

<template>
    <z-paging ref="scrollRef" v-model="listData" @query="getList" :safe-area-inset-bottom="true"
        :use-safe-area-placeholder="true" :show-loading-more-no-more-view="false">
        <view class="p-3">
            <view class="bg-white dark:bg-dark rounded mb-4" v-for="item in listData" :key="item.id" @click="goDetail(item)">
                <view class="flex" v-if="item.coverImg">
                    <wd-img :src="item.coverImg" width="100%" height="100" mode="aspectFill"
                        custom-image="rounded-t"></wd-img>
                </view>
                <view class="p-2">
                    <view class="font-bold text-ellipsis">{{ item.title }}</view>
                    <wd-divider custom-class="!my-1 !p-0" color="rgba(0,0,0,0.1)"></wd-divider>
                    <view hover-class="text-gray-500" class="flex justify-between items-center text-sm text-gray-500">
                        <text class="">{{ item.createdAt }}</text>
                        <view class="">
                            <text>查看详情</text>
                            <wd-icon name="arrow-right" size="16"></wd-icon>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </z-paging>
</template>

<style lang="scss" scoped></style>
