<route lang="jsonc">{
    "layout": "custom",
    "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "景区列表"
    }
}</route>

<script lang="ts" setup>
import { getScenicList } from '@/api/config';
import { useTenantStore } from '@/store';
const tenantStore = useTenantStore()

const scenicList = ref<any>([])
const getScenic = async () => {
    const res: any = await getScenicList({ tenantId: tenantStore.tenantInfo.tenantId })
    if (res.code === 200) {
        scenicList.value = res.data
    }
}
onLoad(() => {
    getScenic()
})
const goDetail = (item: any) => {
    uni.navigateTo({
        url: `/pages/scenic/detail?id=${item.id}`
    })
}
</script>

<template>
    <view class="relative h-vh">
        <view class="flex absolute top-0 left-0 right-0">
            <wd-img src="https://educdn.xjzredu.cn/ticket/test/1/spot/20250722/W5eNQRwM1753155805012.jpg" width="100%"
                mode="widthFix"></wd-img>
        </view>
        <view class="absolute top-[200px] left-0 right-0 bg-[#f6f6f6] dark:bg-black p-3 rounded-t-4 overflow-y-auto"
            style="height: calc(100vh - 247px);">
            <view class="bg-white dark:bg-dark p-2 rounded mb-3" v-for="item in scenicList" :key="item.id"
                @click="goDetail(item)">
                <view class="flex">
                    <view class="w-[80px] h-[80px] flex-shrink-0">
                        <wd-img :src="item.scenicImage.split(',')[0]" width="100%" height="100%"
                            mode="aspectFill"></wd-img>
                    </view>
                    <view class="ml-3 flex flex-col justify-between">
                        <view class="text-base">{{ item.scenicName }}</view>
                        <view>
                            <view class="text-xs text-gray-500">
                                <wd-icon name="time" size="22px"></wd-icon>
                                {{ item.businessHours }}
                            </view>
                            <view class="text-xs text-gray-500">
                                <wd-icon name="location" size="12" class="mr-1"></wd-icon>
                                {{ item.address }}
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<style lang="scss" scoped></style>
