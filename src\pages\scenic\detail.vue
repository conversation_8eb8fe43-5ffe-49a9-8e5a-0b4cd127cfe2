<route lang="jsonc">{
    "layout": "custom",
    "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "景区详情"
    }
}</route>

<script lang="ts" setup>
import { getScenicInfo } from '@/api/scenic';
import { useTenantStore } from '@/store';
import { useSafeAreaInsets } from '@/hooks/useSafeAreaInsets';
const tenantStore = useTenantStore()
const insets = useSafeAreaInsets()
onLoad((options) => {
    getInfo(options.id)
})
const infoData = ref<any>({})
const getInfo = async (id: any) => {
    const res = await getScenicInfo({
        scenicId: id,
        tenantId: tenantStore.tenantInfo.tenantId
    })
    if (res.code === 200) {
        infoData.value = res.data
        infoData.value.scenicImage = [...infoData.value.scenicImage.split(','), ...infoData.value.scenicVideo.split(',')]
    }
}
// 监听页面滚动，需要时必填，在layouts的custom.vue中声明接收页面滚动事件
onPageScroll((e: any) => { })

const openMap = () => {
    uni.openLocation({
        latitude: Number(infoData.value.latitude),  // 纬度，范围为-90~90，负数表示南纬。
        longitude: Number(infoData.value.longitude), // 经度，范围为-180~180，负数表示西经。
        name: infoData.value.scenicName,
        address: infoData.value.address,
        success: (res) => {
            console.log(res);
        },
        fail: (fail) => {
            console.log('fail ', fail)
        },
    });
}

const tabs = ['景区介绍', '游客须知', '交通指南']
const tab = ref(0)
</script>

<template :title="infoData.scenicName">
    <view class="relative h-vh">
        <view class="flex">
            <wd-swiper :list="infoData.scenicImage" height="210px" custom-class="w-full rounded-0"
                :indicator="{ type: 'dots-bar' }" stopAutoplayWhenVideoPlay :autoplayVideo="false" :muted="false">
            </wd-swiper>
        </view>
        <view class="bg-[#f6f6f6] dark:bg-black">
            <view class="p-3 bg-white dark:bg-dark mb-3">
                <view class="flex items-center justify-between mb-2">
                    <view class="text-lg font-bold">{{ infoData.scenicName }}</view>
                    <wd-button type="primary" size="small">去购票</wd-button>
                </view>
                <view class="text-sm text-gray-500 mb-2">
                    <view>开放时间：{{ infoData.businessHours }}</view>
                    <view>联系电话：{{ infoData.contact }}</view>
                </view>
                <view
                    class="bg-[url(https://ticketoss.yunvip123.com/ticket/mapbg.png)] bg-no-repeat bg-center bg-cover h-[48px] flex items-center justify-between px-2">
                    <view class="text-xs">
                        {{ infoData.address }}
                    </view>
                    <view class="text-xs text-center flex-shrink-0" @click="openMap">
                        <view class="bg-white dark:bg-dark rounded-full p-1 flex items-center justify-center mb-0.5">
                            <text class="i-carbon-map" />
                        </view>
                        <text class="text-2xs">地图</text>
                    </view>
                </view>
            </view>
            <view class="bg-white dark:bg-dark">
                <wd-tabs v-model="tab" :auto-line-width="true">
                    <wd-tab v-for="item in tabs" :key="item" :title="item"></wd-tab>
                </wd-tabs>
                <view class="p-3">
                    <view v-if="tab === 0">
                        <rich-text :nodes="infoData.introduce"></rich-text>
                    </view>
                    <view class="text-sm text-gray-500" v-if="tab === 1">
                        {{ infoData.visitorNotice }}
                    </view>
                    <view class="text-sm text-gray-500" v-if="tab === 2">
                        {{ infoData.transportation }}
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<style lang="scss">
swiper {
    border-radius: 0 !important;
}
</style>
