<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "消费记录"
    }
}</route>

<script lang="ts" setup>
import { getPrepaidCardLog } from '@/api/prepaidCard';

import { onPageScroll, onReachBottom } from '@dcloudio/uni-app';
import UseZPaging from "z-paging/components/z-paging/js/hooks/useZPaging"
import { useEnumStore } from '@/store';
const enumStore = useEnumStore();

const scrollRef = ref(null);
UseZPaging(scrollRef)
const listData = ref<any>([]);
const getList = async (pageNo: number, pageSize: number = 10) => {
    try {
        const res: any = await getPrepaidCardLog({
            page: pageNo,
            pageSize: pageSize,
        })
        if (res.code === 200) {
            scrollRef.value?.complete(res.data.list)
        } else {
            scrollRef.value?.complete(false)
            uni.showToast({
                title: '获取数据失败',
                icon: 'none'
            })
        }
    } catch (error) {
        scrollRef.value?.complete(false)
        uni.showToast({
            title: '网络请求异常',
            icon: 'none'
        })
    }
}

const filterText = (arr: any, val: any) => {
    return arr.find(item => item.value === val)?.label || ''
}
</script>

<template>
    <z-paging ref="scrollRef" v-model="listData" @query="getList" :safe-area-inset-bottom="true"
        :use-safe-area-placeholder="true" :show-loading-more-no-more-view="false">
        <view class="p-3">
            <view class="bg-white dark:bg-dark p-2 rounded mb-3 flex items-center justify-between" v-for="item in listData"
                :key="item.id">
                <view class="">
                    <view class="text-md font-bold">{{ filterText(enumStore.enumData?.prepaidCardTradeType.list,
                        item.tradeType) }}<text v-if="filterText(enumStore.enumData?.prepaidCardConsumeType.list,
                            item.consumeType)"> - {{ filterText(enumStore.enumData?.prepaidCardConsumeType.list,
                                item.consumeType) }}</text></view>
                    <view class="text-xs text-gray-500 mt-2">{{ item.createdAt }}</view>
                </view>
                <view class="text-[#fa4350] font-bold text-center">
                    <view><text v-if="item.amount > 0">+</text>{{ item.amount }}</view>
                    <view class="text-xs" v-if="item.giftAmount > 0">（赠：{{ item.giftAmount }}）</view>
                </view>
            </view>
        </view>
    </z-paging>
</template>

<style lang="scss" scoped></style>
