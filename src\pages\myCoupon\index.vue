<route lang="jsonc" type="page">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "我的优惠券"
    }
}</route>

<script lang="ts" setup>
import { getUserCouponList } from "@/api/coupon";
import { onPageScroll, onReachBottom } from '@dcloudio/uni-app';
import UseZPaging from "z-paging/components/z-paging/js/hooks/useZPaging"

const currentTab = ref(0)
const tabsList = ref(['待使用', '已使用', '已过期', '已作废'])

const tabsChange = (index: number) => {
    scrollRef.value?.reload()
}

const scrollRef = ref(null)
UseZPaging(scrollRef)
const listData = ref<any>([])
const getList = async (pageNo: number, pageSize: number = 10) => {
    try {
        const res: any = await getUserCouponList({
            page: pageNo,
            pageSize: pageSize,
            useStatus: currentTab.value
        })
        if (res.code === 200) {
            scrollRef.value?.complete(res.data.list)
        } else {
            scrollRef.value?.complete(false)
            uni.showToast({
                title: '获取数据失败',
                icon: 'none'
            })
        }
    } catch (error) {
        scrollRef.value?.complete(false)
        uni.showToast({
            title: '网络请求异常',
            icon: 'none'
        })
    }
}

const turnValidType = (val: any) => {
    if (val.validType == 1) {
        return '永久有效'
    } else {
        return val.validBeginDate + ' 至 ' + val.validEndDate
    }
}
const filterContent = (val: any) => {
    return val.map((item: any) => item.ticketName).join(',')
}
const handleUse = (val: any) => {
    uni.showToast({
        title: '功能暂未开发',
        icon: 'none'
    })
}
</script>

<template>
    <z-paging ref="scrollRef" v-model="listData" @query="getList" :safe-area-inset-bottom="true"
        :use-safe-area-placeholder="true" :show-loading-more-no-more-view="false">
        <template #top>
            <view>
                <wd-tabs v-model="currentTab" @change="tabsChange">
                    <block v-for="item in tabsList" :key="item">
                        <wd-tab :title="`${item}`"></wd-tab>
                    </block>
                </wd-tabs>
            </view>
        </template>
        <view class="p-3" v-if="listData && listData.length">
            <view class="flex items-center mb-3 rounded-3" v-for="item in listData" :key="item.id"
                :class="item.useStatus == 0 ? 'bg-[#FFF4F1] dark:bg-dark-1' : 'bg-[#E5E6EB] dark:bg-dark-1 grayscale'">
                <view class="w-[100px] text-center text-[#fa4350]">
                    <view class="text-xl font-bold mb-1 leading-none">
                        <text v-if="item.couponInfo.couponType == 1" class="text-xs">￥</text>{{
                            item.couponInfo.couponValue
                        }}<text v-if="item.couponInfo.couponType != 1" class="text-xs">折</text>
                    </view>
                    <view class="text-xs">{{ item.couponInfo.thresholdType == 2 ? '满' + item.couponInfo.thresholdValue +
                        '可用' :
                        '无门槛'
                    }}</view>
                </view>
                <view class="flex-1 bg-white dark:bg-dark rounded-3 p-4">
                    <view class="pr-3 flex items-center mb-2">
                        <wd-tag :type="item.couponInfo.couponType == 1 ? 'danger' : 'warning'"
                            custom-class="!rounded-1 !px-1 !py-0.5 !text-xs">{{
                                item.couponInfo.couponType
                                    == 1 ? '满减券' : '折扣券' }}</wd-tag>
                        <view class="ml-2 text-md font-bold">{{ item.couponInfo.couponName }}</view>
                    </view>
                    <view class="flex items-center justify-between">
                        <view class="text-sm">
                            <view class="text-gray-700 text-xs mb-2">{{ turnValidType(item) }}</view>
                            <view class="text-gray-500 text-xs">{{ item.couponInfo.ticketScopeType == 1 ? '全部可用' :
                                '部分可用' }}
                                <wd-tooltip placement="top" :content="filterContent(item.couponInfo.ticketScopeList)"
                                    :show-close="true" v-if="item.couponInfo.ticketScopeType == 2">
                                    <wd-icon name="info-circle" size="25px"></wd-icon>
                                </wd-tooltip>
                            </view>
                        </view>
                        <view>
                            <wd-button type="primary" size="small" v-if="item.useStatus == 0"
                                @click="handleUse(item)">去使用</wd-button>
                            <view
                                class="text-xs border border-solid border-1 border-primary rounded-1 py-0.5 px-1 transform rotate-25"
                                v-if="item.useStatus == 1">已使用</view>
                            <view
                                class="text-xs border border-solid border-1 border-primary rounded-1 py-0.5 px-1 transform rotate-25"
                                v-if="item.useStatus == 2">已过期</view>
                            <view
                                class="text-xs border border-solid border-1 border-primary rounded-1 py-0.5 px-1 transform rotate-25"
                                v-if="item.useStatus == 3">已作废</view>
                        </view>
                    </view>
                </view>

            </view>
        </view>
    </z-paging>
</template>

<style lang="scss" scoped>
.couponCard {
    -webkit-mask-image: radial-gradient(circle at 100px 10px, transparent 10px, red 10.5px), radial-gradient(closest-side circle at 50%, red 99%, transparent 100%);
    -webkit-mask-size: 100%, 4px 12px;
    -webkit-mask-repeat: repeat, repeat-y;
    -webkit-mask-position: 0 -10px, 100px;
    -webkit-mask-composite: source-out;
    mask-composite: subtract;
}
</style>
