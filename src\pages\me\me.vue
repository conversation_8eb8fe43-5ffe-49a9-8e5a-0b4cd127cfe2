<route lang="jsonc">{
  "layout": "default",
  "style": {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    // "navigationStyle": "custom",
    "navigationBarTitleText": "我的"
  }
}</route>

<script lang="ts" setup>
import type { IUploadSuccessInfo } from '@/api/types/login'
import { storeToRefs } from 'pinia'
import { getUserInfo } from '@/api/login'
import { getConfigByScene } from '@/api/config'
import { LOGIN_PAGE } from '@/router/config'
import { useUserStore, useTokenStore, useTenantStore } from '@/store'

const userStore = useUserStore()
// 使用storeToRefs解构userInfo
const { userInfo } = storeToRefs(userStore)

onLoad(() => {
  // getInfo()
  getConfig()
})
// #ifdef MP-WEIXIN
async function getInfo() {
  const res = await getUserInfo()
  useUserStore().setUserInfo(res.data)
}
// #endif

// 获取小程序配置
const configInfo = ref<any>({})
const getConfig = async () => {
  const res = await getConfigByScene({ scene: 'miniProgram', tenantId: useTenantStore().tenantInfo.tenantId })
  if (res.code === 200) {
    configInfo.value = res.data
  }
}
// 查看用户信息
function handleEditProfile() {
  uni.navigateTo({
    url: '/pages/me/editUserInfo',
  })
}

// 联系客服or投诉电话
const showAction = (type: number) => {
  let msg = type == 1 ? configInfo.value.customerHotline : configInfo.value.complaintHotline
  uni.makePhoneCall({
    phoneNumber: msg
  })
}
// 退出登录
function handleLogout() {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清空用户信息
        useTokenStore().logout()
        // 执行退出登录逻辑
        uni.showToast({
          title: '退出登录成功',
          icon: 'success',
        })
        // #ifdef MP-WEIXIN
        // 微信小程序，去首页
        uni.reLaunch({ url: '/pages/index/index' })
        // #endif
        // #ifndef MP-WEIXIN
        // 非微信小程序，去登录页
        uni.navigateTo({ url: LOGIN_PAGE })
        // #endif
      }
    },
  })
}

function goToPage(url: string) {
  uni.navigateTo({ url })
}
</script>

<template>
  <view class="p-3 light:bg-[#f6f6f6]">
    <view class="flex items-center mb-4">
      <wd-img :src="userInfo.avatar" round width="90" height="90"></wd-img>
      <view class="ml-5">
        <view class="text-xl mb-1" @click="goToPage(LOGIN_PAGE)">{{ userInfo.nickname || '点击登录' }}</view>
        <view class="text-sm c-coolGray" @click="handleEditProfile">查看并编辑个人资料</view>
      </view>
    </view>
    <view class="bg-white dark:bg-dark p-2 mb-4 rounded">
      <view class="font-bold mb-3">我的订单</view>
      <view class="grid grid-cols-4 gap-4">
        <view class="text-center" @click="goToPage('/pages/ticketOrder/index')">
          <wd-icon name="image" custom-class="text-[50px] mb-1"></wd-icon>
          <view class="text-[12px]">门票订单</view>
        </view>
        <view class="text-center">
          <wd-icon name="image" custom-class="text-[50px] mb-1"></wd-icon>
          <view class="text-[12px]">租赁订单</view>
        </view>
      </view>
    </view>
    <view class="bg-white dark:bg-dark p-2 mb-8 rounded">
      <view class="font-bold mb-3">更多服务</view>
      <view class="grid grid-cols-4 gap-4">
        <view class="text-center" @click="goToPage('/pages/tourist/index')">
          <wd-icon name="image" custom-class="text-[50px] mb-1"></wd-icon>
          <view class="text-[12px]">游客信息</view>
        </view>
        <view class="text-center">
          <wd-icon name="image" custom-class="text-[50px] mb-1"></wd-icon>
          <view class="text-[12px]">游玩卡</view>
        </view>
        <view class="text-center" @click="goToPage('/pages/prepaidCard/index')">
          <wd-icon name="image" custom-class="text-[50px] mb-1"></wd-icon>
          <view class="text-[12px]">一卡通</view>
        </view>
        <view class="text-center" @click="goToPage('/pages/couponCenter/index')">
          <wd-icon name="image" custom-class="text-[50px] mb-1"></wd-icon>
          <view class="text-[12px]">领券中心</view>
        </view>
        <view class="text-center" @click="goToPage('/pages/myCoupon/index')">
          <wd-icon name="image" custom-class="text-[50px] mb-1"></wd-icon>
          <view class="text-[12px]">我的优惠券</view>
        </view>
        <view class="text-center" @click="goToPage('/pages/complaint/index')">
          <wd-icon name="image" custom-class="text-[50px] mb-1"></wd-icon>
          <view class="text-[12px]">投诉建议</view>
        </view>
        <!-- 电话客服 -->
        <view class="text-center" @click="showAction(1)" v-if="configInfo.showCustomer && configInfo.customerType == 1">
          <wd-icon name="image" custom-class="text-[50px] mb-1"></wd-icon>
          <view class="text-[12px]">联系客服</view>
        </view>
        <!-- 微信客服会话：只能用button触发 -->
        <view class="text-center relative overflow-hidden"
          v-if="configInfo.showCustomer && configInfo.customerType == 2">
          <wd-button type="primary" custom-class="!absolute z-10 top-0 left-0 opacity-0 !w-full !h-full"
            open-type="contact" :session-from="userInfo.phone"></wd-button>
          <wd-icon name="image" custom-class="text-[50px] mb-1"></wd-icon>
          <view class="text-[12px]">联系客服</view>
        </view>
        <view class="text-center" @click="showAction(2)" v-if="configInfo.showComplaint">
          <wd-icon name="image" custom-class="text-[50px] mb-1"></wd-icon>
          <view class="text-[12px]">投诉电话</view>
        </view>
      </view>
    </view>
    <view class="text-center w-full" v-if="useTokenStore().hasLogin">
      <wd-button type="primary" size="large" block @click="handleLogout">
        退出登录
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss"></style>
