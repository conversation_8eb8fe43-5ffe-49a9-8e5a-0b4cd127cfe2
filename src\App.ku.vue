<script setup lang="ts">
import { ref } from 'vue'
import FgTabbar from '@/tabbar/index.vue'
import { isPageTabbar } from './tabbar/store'
import { currRoute } from './utils'
import { useTheme } from '@/composables/useTheme'
const { theme, themeVars } = useTheme()

const isCurrentPageTabbar = ref(true)
onShow(() => {
  console.log('App.ku.vue onShow', currRoute())
  const { path } = currRoute()
  isCurrentPageTabbar.value = isPageTabbar(path)
})

const helloKuRoot = ref('Hello AppKuVue')

const exposeRef = ref('this is form app.Ku.vue')

defineExpose({
  exposeRef,
})
</script>

<template>
  <view class="h-full" :class="theme == 'dark' ? 'dark-bg' : 'light-bg'">
    <!-- 这个先隐藏了，知道这样用就行 -->
    <view class="hidden text-center">
      {{ helloKuRoot }}，这里可以配置全局的东西
    </view>
    <wd-config-provider :theme-vars="themeVars" :theme="theme" :custom-class="`${theme}`">
      <KuRootView />
    </wd-config-provider>

    <FgTabbar v-if="isCurrentPageTabbar" />
    <wd-toast />
    <wd-message-box />
  </view>
</template>

<style>
page {
  height: 100%;
}

.dark-bg {
  background-color: #000000;
}

.light-bg {
  background-color: #f6f6f6;
}
</style>